import 'dart:typed_data';
import 'package:encrypt/encrypt.dart' as encryptLib;
import 'dart:convert';
import 'package:crypto/crypto.dart';

/// خدمة التشفير AES المتوافقة مع SAS Radius 4
/// تستخدم نفس آلية التشفير المستخدمة في PHP connector
class AESService {
  
  /// تشفير البيانات باستخدام AES-256-CBC
  /// [data] البيانات المراد تشفيرها
  /// [passphrase] كلمة المرور للتشفير (يجب أن تكون 32 حرف)
  /// إرجاع البيانات المشفرة بصيغة base64
  static String encrypt({required String data, required String passphrase}) {
    try {
      // إضافة salt ثابت (نفس المستخدم في PHP)
      var salt = [1, 2, 3, 4, 5, 6, 7, 8];

      // توليد المفتاح و IV باستخدام PBKDF1 (MD5)
      List<int> salted = [];
      List<int> dx = [];
      
      while (salted.length < 48) {
        dx = _generateMd5(dx + utf8.encode(passphrase) + salt.toList());
        salted = salted + dx;
      }

      // استخراج المفتاح (32 بايت) و IV (16 بايت)
      var key = salted.sublist(0, 32);
      var iv = salted.sublist(32, 48);

      // تشفير البيانات
      final kkey = encryptLib.Key(Uint8List.fromList(key));
      final iiv = encryptLib.IV(Uint8List.fromList(iv));

      final encrypter = encryptLib.Encrypter(
          encryptLib.AES(kkey, mode: encryptLib.AESMode.cbc));
      final encrypted = encrypter.encrypt(data, iv: iiv);
      
      var aesEncode = encrypted.bytes;
      
      // تكوين النتيجة النهائية بنفس تنسيق OpenSSL
      String encoded = base64.encode(
          utf8.encode("Salted__") + salt.toList() + aesEncode.toList());
      
      return encoded;
    } catch (e) {
      throw Exception('خطأ في تشفير البيانات: ${e.toString()}');
    }
  }

  /// فك تشفير البيانات
  /// [base64Data] البيانات المشفرة بصيغة base64
  /// [passphrase] كلمة المرور لفك التشفير
  /// إرجاع البيانات المفكوكة التشفير
  static String decrypt({required String base64Data, required String passphrase}) {
    try {
      // فك تشفير base64
      var data = base64.decode(base64Data);
      
      // التحقق من وجود "Salted__" في بداية البيانات
      if (data.length < 16 || String.fromCharCodes(data.sublist(0, 8)) != "Salted__") {
        throw Exception('تنسيق البيانات المشفرة غير صحيح');
      }
      
      // استخراج salt والبيانات المشفرة
      var salt = data.sublist(8, 16);
      var ct = data.sublist(16);
      
      // توليد المفتاح و IV
      List<int> salted = [];
      List<int> dx = [];
      
      while (salted.length < 48) {
        dx = _generateMd5(dx + utf8.encode(passphrase) + salt);
        salted = salted + dx;
      }
      
      var key = salted.sublist(0, 32);
      var iv = salted.sublist(32, 48);
      
      // فك التشفير
      final kkey = encryptLib.Key(Uint8List.fromList(key));
      final iiv = encryptLib.IV(Uint8List.fromList(iv));
      
      final encrypter = encryptLib.Encrypter(
          encryptLib.AES(kkey, mode: encryptLib.AESMode.cbc));
      
      final decrypted = encrypter.decryptBytes(
          encryptLib.Encrypted(Uint8List.fromList(ct)), iv: iiv);
      
      return utf8.decode(decrypted);
    } catch (e) {
      throw Exception('خطأ في فك تشفير البيانات: ${e.toString()}');
    }
  }

  /// توليد hash MD5 للبيانات المدخلة
  static List<int> _generateMd5(List<int> input) {
    return md5.convert(input).bytes;
  }

  static const String encryptionPassphrase = 'abcdefghijuklmno0123456789012345';
}