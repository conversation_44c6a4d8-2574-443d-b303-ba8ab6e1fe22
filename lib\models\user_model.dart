class UserModel {
  final String adminId;
  final String id;
  final String username;
  final String password;
  final String phoneNumber;
  final UserRole role;
  final String fullName;
  final DateTime createdAt;
  final bool isActive;
  final List<String> permissions; // قائمة الصلاحيات

  UserModel({
    required this.id,
    required this.username,
    required this.password,
    required this.phoneNumber,
    required this.role,
    required this.fullName,
    required this.createdAt,
    this.isActive = true,
    required this.permissions,
    required this.adminId,
  });

  // Firebase compatibility methods
  Map<String, dynamic> toMap() => {
    'id': id,
    'username': username,
    'password': password,
    'phoneNumber': phoneNumber,
    'role': role.toString(),
    'fullName': fullName,
    'createdAt': createdAt.toIso8601String(),
    'isActive': isActive,
    'permissions': permissions,
    'adminId': adminId,
  };

  factory UserModel.fromMap(Map<String, dynamic> map) => UserModel(
    id: map['id'],
    username: map['username'],
    password: map['password'],
    phoneNumber: map['phoneNumber'],
    role: UserRole.values.firstWhere(
      (e) => e.toString() == map['role'],
      orElse: () => UserRole.support,
    ),
    fullName: map['fullName'],
    createdAt: DateTime.parse(map['createdAt']),
    isActive:
        map['isActive'] == 1 ||
        map['isActive'] == true ||
        map['isActive'] == null,
    permissions: List<String>.from(map['permissions'] ?? []),
    adminId: map["adminId"] ?? map["id"],
  );

  // Legacy compatibility methods
  Map<String, dynamic> toJson() => toMap();

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      UserModel.fromMap(json);

  UserModel copyWith({
    String? id,
    String? username,
    String? password,
    String? phoneNumber,
    UserRole? role,
    String? fullName,
    DateTime? createdAt,
    bool? isActive,
    List<String>? permissions,
  }) => UserModel(
    adminId: adminId,
    id: id ?? this.id,
    username: username ?? this.username,
    password: password ?? this.password,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    role: role ?? this.role,
    fullName: fullName ?? this.fullName,
    createdAt: createdAt ?? this.createdAt,
    isActive: isActive ?? this.isActive,
    permissions: permissions ?? this.permissions,
  );

  String get roleDisplayName {
    switch (role) {
      case UserRole.admin:
        return 'مدير';
      case UserRole.distributor:
        return 'موزع';
      case UserRole.support:
        return 'دعم فني';
      case UserRole.sales:
        return 'مبيعات';
      case UserRole.accountant:
        return 'محاسب';
      case UserRole.technician:
        return 'فني';
      case UserRole.operator:
        return 'مشغل';
    }
  }

  // التحقق من الصلاحيات
  bool hasPermission(String permission) {
    // المدير له جميع الصلاحيات
    if (role == UserRole.admin) return true;

    // التحقق من الصلاحيات المحددة
    return permissions.contains(permission);
  }

  // الحصول على الصلاحيات الافتراضية حسب الدور
  static List<String> getDefaultPermissions(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Permission.allPermissions;
      case UserRole.distributor:
        return [
          Permission.viewSubscribers,
          Permission.addSubscribers,
          Permission.editSubscribers,
          Permission.viewPackages,
          Permission.viewReports,
        ];
      case UserRole.support:
        return [
          Permission.viewSubscribers,
          Permission.editSubscribers,
          Permission.viewPackages,
          Permission.viewActivityLogs,
        ];
      case UserRole.sales:
        return [
          Permission.viewSubscribers,
          Permission.addSubscribers,
          Permission.editSubscribers,
          Permission.viewPackages,
          Permission.viewReports,
        ];
      case UserRole.accountant:
        return [
          Permission.viewSubscribers,
          Permission.viewPaymentRecords,
          Permission.addPaymentRecords,
          Permission.viewExpenses,
          Permission.addExpenses,
          Permission.viewReports,
        ];
      case UserRole.technician:
        return [
          Permission.viewSubscribers,
          Permission.editSubscribers,
          Permission.viewNetworkDevices,
          Permission.editNetworkDevices,
          Permission.rebootNetworkDevices,
          Permission.viewActivityLogs,
        ];
      case UserRole.operator:
        return [
          Permission.viewSubscribers,
          Permission.viewPackages,
          Permission.viewReports,
        ];
    }
  }
}

enum UserRole {
  admin, // مدير - صلاحيات كاملة
  distributor, // موزع - إدارة المشتركين والباقات
  support, // دعم فني - دعم المشتركين
  sales, // مبيعات - إضافة وتعديل المشتركين
  accountant, // محاسب - إدارة المدفوعات والمصاريف
  technician, // فني - إدارة الأجهزة والشبكة
  operator, // مشغل - عرض البيانات فقط
}

// تعريف الصلاحيات
class Permission {
  // الصلاحيات الأساسية
  static const String viewSubscribers = 'view_subscribers';
  static const String addSubscribers = 'add_subscribers';
  static const String editSubscribers = 'edit_subscribers';
  static const String deleteSubscribers = 'delete_subscribers';

  // صلاحيات الباقات
  static const String viewPackages = 'view_packages';
  static const String addPackages = 'add_packages';
  static const String editPackages = 'edit_packages';
  static const String deletePackages = 'delete_packages';

  // صلاحيات المدفوعات
  static const String viewPaymentRecords = 'view_payment_records';
  static const String addPaymentRecords = 'add_payment_records';
  static const String editPaymentRecords = 'edit_payment_records';
  static const String deletePaymentRecords = 'delete_payment_records';

  // صلاحيات المصاريف
  static const String viewExpenses = 'view_expenses';
  static const String addExpenses = 'add_expenses';
  static const String editExpenses = 'edit_expenses';
  static const String deleteExpenses = 'delete_expenses';

  // صلاحيات الأجهزة
  static const String viewNetworkDevices = 'view_network_devices';
  static const String addNetworkDevices = 'add_network_devices';
  static const String editNetworkDevices = 'edit_network_devices';
  static const String deleteNetworkDevices = 'delete_network_devices';
  static const String rebootNetworkDevices = 'reboot_network_devices';

  // صلاحيات المستخدمين
  static const String viewUsers = 'view_users';
  static const String addUsers = 'add_users';
  static const String editUsers = 'edit_users';
  static const String deleteUsers = 'delete_users';

  // صلاحيات التقارير
  static const String viewReports = 'view_reports';
  static const String exportReports = 'export_reports';

  // صلاحيات سجلات النشاط
  static const String viewActivityLogs = 'view_activity_logs';
  static const String deleteActivityLogs = 'delete_activity_logs';

  // صلاحيات الإعدادات
  static const String viewSettings = 'view_settings';
  static const String editSettings = 'edit_settings';

  // قائمة جميع الصلاحيات
  static const List<String> allPermissions = [
    viewSubscribers,
    addSubscribers,
    editSubscribers,
    deleteSubscribers,
    viewPackages,
    addPackages,
    editPackages,
    deletePackages,
    viewPaymentRecords,
    addPaymentRecords,
    editPaymentRecords,
    deletePaymentRecords,
    viewExpenses,
    addExpenses,
    editExpenses,
    deleteExpenses,
    viewNetworkDevices,
    addNetworkDevices,
    editNetworkDevices,
    deleteNetworkDevices,
    rebootNetworkDevices,
    viewUsers,
    addUsers,
    editUsers,
    deleteUsers,
    viewReports,
    exportReports,
    viewActivityLogs,
    deleteActivityLogs,
    viewSettings,
    editSettings,
  ];

  // الحصول على اسم الصلاحية بالعربية
  static String getDisplayName(String permission) {
    switch (permission) {
      case viewSubscribers:
        return 'عرض المشتركين';
      case addSubscribers:
        return 'إضافة مشتركين';
      case editSubscribers:
        return 'تعديل المشتركين';
      case deleteSubscribers:
        return 'حذف المشتركين';
      case viewPackages:
        return 'عرض الباقات';
      case addPackages:
        return 'إضافة باقات';
      case editPackages:
        return 'تعديل الباقات';
      case deletePackages:
        return 'حذف الباقات';
      case viewPaymentRecords:
        return 'عرض المدفوعات';
      case addPaymentRecords:
        return 'إضافة مدفوعات';
      case editPaymentRecords:
        return 'تعديل المدفوعات';
      case deletePaymentRecords:
        return 'حذف المدفوعات';
      case viewExpenses:
        return 'عرض المصاريف';
      case addExpenses:
        return 'إضافة مصاريف';
      case editExpenses:
        return 'تعديل المصاريف';
      case deleteExpenses:
        return 'حذف المصاريف';
      case viewNetworkDevices:
        return 'عرض الأجهزة';
      case addNetworkDevices:
        return 'إضافة أجهزة';
      case editNetworkDevices:
        return 'تعديل الأجهزة';
      case deleteNetworkDevices:
        return 'حذف الأجهزة';
      case rebootNetworkDevices:
        return 'إعادة تشغيل الأجهزة';
      case viewUsers:
        return 'عرض المستخدمين';
      case addUsers:
        return 'إضافة مستخدمين';
      case editUsers:
        return 'تعديل المستخدمين';
      case deleteUsers:
        return 'حذف المستخدمين';
      case viewReports:
        return 'عرض التقارير';
      case exportReports:
        return 'تصدير التقارير';
      case viewActivityLogs:
        return 'عرض سجلات النشاط';
      case deleteActivityLogs:
        return 'حذف سجلات النشاط';
      case viewSettings:
        return 'عرض الإعدادات';
      case editSettings:
        return 'تعديل الإعدادات';
      default:
        return permission;
    }
  }
}
