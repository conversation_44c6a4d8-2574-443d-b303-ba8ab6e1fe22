// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_subscription_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceSubscription _$DeviceSubscriptionFromJson(Map<String, dynamic> json) =>
    DeviceSubscription(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      deviceId: json['device_id'] as String,
      deviceName: json['device_name'] as String?,
      deviceModel: json['device_model'] as String?,
      deviceBrand: json['device_brand'] as String?,
      androidVersion: json['android_version'] as String?,
      appVersion: json['app_version'] as String?,
      accountNumber: json['account_number'] as String,
      subscriptionStartDate: DateTime.parse(
        json['subscription_start_date'] as String,
      ),
      subscriptionEndDate: DateTime.parse(
        json['subscription_end_date'] as String,
      ),
      isActive: json['is_active'] as bool,
      lastAccess: DateTime.parse(json['last_access'] as String),
      accessCount: (json['access_count'] as num).toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$DeviceSubscriptionToJson(
  DeviceSubscription instance,
) => <String, dynamic>{
  'id': instance.id,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
  'device_id': instance.deviceId,
  'device_name': instance.deviceName,
  'device_model': instance.deviceModel,
  'device_brand': instance.deviceBrand,
  'android_version': instance.androidVersion,
  'app_version': instance.appVersion,
  'account_number': instance.accountNumber,
  'subscription_start_date': instance.subscriptionStartDate.toIso8601String(),
  'subscription_end_date': instance.subscriptionEndDate.toIso8601String(),
  'is_active': instance.isActive,
  'last_access': instance.lastAccess.toIso8601String(),
  'access_count': instance.accessCount,
  'notes': instance.notes,
};
