import 'package:flutter/material.dart';

enum SyncOperationType {
  connection,
  login,
  fetchPackages,
  fetchSubscribers,
  saveSubscriber,
  savePackage,
  error,
  success,
  info,
}

enum SyncStatus {
  pending,
  running,
  completed,
  failed,
}

class SyncOperation {
  final String id;
  final SyncOperationType type;
  final String message;
  final SyncStatus status;
  final DateTime timestamp;
  final String? details;
  final IconData icon;
  final Color color;
  final int? progress;
  final int? total;

  SyncOperation({
    required this.id,
    required this.type,
    required this.message,
    required this.status,
    required this.timestamp,
    this.details,
    required this.icon,
    required this.color,
    this.progress,
    this.total,
  });

  factory SyncOperation.connection({
    required String message,
    required SyncStatus status,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.connection,
      message: message,
      status: status,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.wifi,
      color: status == SyncStatus.completed ? Colors.green : 
             status == SyncStatus.failed ? Colors.red : Colors.blue,
    );
  }

  factory SyncOperation.login({
    required String message,
    required SyncStatus status,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.login,
      message: message,
      status: status,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.login,
      color: status == SyncStatus.completed ? Colors.green : 
             status == SyncStatus.failed ? Colors.red : Colors.blue,
    );
  }

  factory SyncOperation.fetchPackages({
    required String message,
    required SyncStatus status,
    String? details,
    int? progress,
    int? total,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.fetchPackages,
      message: message,
      status: status,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.inventory,
      color: status == SyncStatus.completed ? Colors.green : 
             status == SyncStatus.failed ? Colors.red : Colors.orange,
      progress: progress,
      total: total,
    );
  }

  factory SyncOperation.fetchSubscribers({
    required String message,
    required SyncStatus status,
    String? details,
    int? progress,
    int? total,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.fetchSubscribers,
      message: message,
      status: status,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.people,
      color: status == SyncStatus.completed ? Colors.green : 
             status == SyncStatus.failed ? Colors.red : Colors.purple,
      progress: progress,
      total: total,
    );
  }

  factory SyncOperation.saveSubscriber({
    required String subscriberName,
    required SyncStatus status,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.saveSubscriber,
      message: 'حفظ مشترك: $subscriberName',
      status: status,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.person_add,
      color: status == SyncStatus.completed ? Colors.green : 
             status == SyncStatus.failed ? Colors.red : Colors.teal,
    );
  }

  factory SyncOperation.savePackage({
    required String packageName,
    required SyncStatus status,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.savePackage,
      message: 'حفظ باقة: $packageName',
      status: status,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.save,
      color: status == SyncStatus.completed ? Colors.green : 
             status == SyncStatus.failed ? Colors.red : Colors.indigo,
    );
  }

  factory SyncOperation.error({
    required String message,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.error,
      message: message,
      status: SyncStatus.failed,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.error,
      color: Colors.red,
    );
  }

  factory SyncOperation.success({
    required String message,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.success,
      message: message,
      status: SyncStatus.completed,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.check_circle,
      color: Colors.green,
    );
  }

  factory SyncOperation.info({
    required String message,
    String? details,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: SyncOperationType.info,
      message: message,
      status: SyncStatus.completed,
      timestamp: DateTime.now(),
      details: details,
      icon: Icons.info,
      color: Colors.blue,
    );
  }

  String get statusText {
    switch (status) {
      case SyncStatus.pending:
        return 'في الانتظار';
      case SyncStatus.running:
        return 'جاري التنفيذ';
      case SyncStatus.completed:
        return 'مكتمل';
      case SyncStatus.failed:
        return 'فشل';
    }
  }

  String get progressText {
    if (progress != null && total != null) {
      return '$progress/$total';
    }
    return '';
  }

  double get progressValue {
    if (progress != null && total != null && total! > 0) {
      return progress! / total!;
    }
    return 0.0;
  }
} 