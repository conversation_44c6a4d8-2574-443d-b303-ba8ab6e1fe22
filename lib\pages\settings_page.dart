// filepath: e:\ISP\isp_manager\lib\pages\settings_page.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
// Import for debugPrint
import 'package:isp_manager/pages/firebase_auth_page.dart';
import 'package:local_auth/local_auth.dart'; // Import for local_auth
import 'package:shared_preferences/shared_preferences.dart'; // Import for SharedPreferences
import 'package:supabase_flutter/supabase_flutter.dart'; // Import for Supabase
import '../services/database_service.dart';
import '../services/device_subscription_service.dart'; // Import device subscription service
import '../services/firebase_auth_service.dart'; // Import Firebase Auth Service
import '../models/user_model.dart';
import '../models/device_subscription_model.dart'; // Import device subscription model
import 'message_templates_page.dart';
import 'bulk_import_page.dart';
import 'activity_log_page.dart';
import 'telegram_settings_page.dart';
import 'debt_reminder_page.dart'; // Import DebtReminderPage
import 'printer_settings_page.dart';
import 'sas_servers_page.dart'; // Updated import
import 'mikrotik_settings_page.dart'; // New import for MikroTik settings
import 'currency_country_settings_page.dart'; // New import for Currency and Country settings
import 'backup_restore_page.dart'; // New import for BackupRestorePage
import 'renew_subscription_page.dart'; // New import for RenewSubscriptionPage
import 'package:isp_manager/pages/profile_page.dart'; // New import for ProfilePage
import 'users_management_page.dart'; // New import for Users Management
import 'vpn_settings_page.dart'; // New import for VPN Settings

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage>
    with TickerProviderStateMixin {
  UserModel? _currentUser;
  DeviceSubscription? _deviceSubscription; // New: Device subscription info
  bool _isLoading = true;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  final LocalAuthentication _localAuth =
      LocalAuthentication(); // LocalAuth instance
  bool _isBiometricEnabled = false; // State for biometric lock
  late DeviceSubscriptionService
  _deviceSubscriptionService; // New: Device subscription service

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _loadData();
    _loadBiometricSetting(); // Load biometric setting
    // New: Load subscription info
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final authService = FirebaseAuthService();
      final firebaseUser = authService.currentUser;
      if (firebaseUser != null) {
        final userData = await authService.getUserData(firebaseUser.uid);
        setState(() {
          _currentUser = userData;
          _isLoading = false;
        });
      } else {
        setState(() {
          _currentUser = null;
          _isLoading = false;
        });
      }
      _fadeController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
    }
    await _loadSubscriptionInfo();
  }

  // New: Load device subscription information
  Future<void> _loadSubscriptionInfo() async {
    final prefs = await SharedPreferences.getInstance();
    _deviceSubscriptionService = DeviceSubscriptionService(
      Supabase.instance.client,
      prefs,
    );
    final subscription = await _deviceSubscriptionService
        .getDeviceSubscription();
    setState(() {
      _deviceSubscription = subscription;
    });
  }

  Future<void> _loadBiometricSetting() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isBiometricEnabled = prefs.getBool('isBiometricEnabled') ?? false;
    });
  }

  Future<void> _toggleBiometricAuth(bool newValue) async {
    if (newValue) {
      // If enabling, check for available biometrics and authenticate
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        if (!mounted) return;
        _showErrorDialog(
          'لا توجد بصمة أو وجه',
          'لا يتوفر لديك أي نوع من أنواع المصادقة البيومترية (بصمة أو وجه) على جهازك.',
        );
        setState(() => _isBiometricEnabled = false); // Revert switch
        return;
      }

      final List<BiometricType> availableBiometrics = await _localAuth
          .getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        if (!mounted) return;
        _showErrorDialog(
          'لا توجد بصمة أو وجه',
          'لا يتوفر لديك أي نوع من أنواع المصادقة البيومترية (بصمة أو وجه) على جهازك.',
        );
        setState(() => _isBiometricEnabled = false); // Revert switch
        return;
      }

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'يرجى المصادقة لتفعيل قفل التطبيق بالبصمة/الوجه',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );

      if (didAuthenticate) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('isBiometricEnabled', true);
        setState(() => _isBiometricEnabled = true);
        if (!mounted) return;
        _showSuccessDialog(
          'تم التفعيل',
          'تم تفعيل قفل التطبيق بنجاح باستخدام البصمة/الوجه.',
          Icons.lock_open,
        );
      } else {
        if (!mounted) return;
        _showErrorDialog(
          'فشل المصادقة',
          'لم تتم المصادقة بنجاح. لم يتم تفعيل قفل التطبيق.',
        );
        setState(() => _isBiometricEnabled = false); // Revert switch
      }
    } else {
      // If disabling, just update the setting
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isBiometricEnabled', false);
      setState(() => _isBiometricEnabled = false);
      if (!mounted) return;
      _showSuccessDialog(
        'تم التعطيل',
        'تم تعطيل قفل التطبيق بنجاح.',
        Icons.lock,
      );
    }
  }

  Future<void> _resetApp() async {
    final confirmed = await _showConfirmationDialog(
      'إعادة ضبط التطبيق',
      'سيتم حذف جميع البيانات وإعادة التطبيق إلى الإعدادات الافتراضية. هذا الإجراء لا يمكن التراجع عنه!',
      Icons.warning,
      'إعادة ضبط',
      isDestructive: true,
    );

    if (confirmed == true) {
      try {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) {
            Future(() async {
              await DatabaseService().resetDatabase();
              await DatabaseService().deleteDocsByAdminIdAcrossCollections();
              Navigator.of(dialogContext).pop(); // ✅ دا هيقفل الـ dialog صح
            });
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      color: Theme.of(dialogContext).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري إعادة ضبط التطبيق...',
                      style: Theme.of(dialogContext).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          },
        );

        // Navigate to login page
        Navigator.of(context).pushAndRemoveUntil(
          PageRouteBuilder(
            pageBuilder:
                (
                  BuildContext context,
                  Animation<double> animation,
                  Animation<double> secondaryAnimation,
                ) => const FirebaseAuthPage(),
            transitionsBuilder:
                (
                  BuildContext context,
                  Animation<double> animation,
                  Animation<double> secondaryAnimation,
                  Widget child,
                ) {
                  return FadeTransition(opacity: animation, child: child);
                },
            transitionDuration: const Duration(milliseconds: 500),
          ),
          (route) => false,
        );
      } catch (e) {
        if (!mounted) return;
        print(e);
        _showErrorDialog('فشل في إعادة ضبط التطبيق', e.toString());
      }
    }
  }

  Future<void> _logout() async {
    final confirmed = await _showConfirmationDialog(
      'تسجيل الخروج',
      'هل أنت متأكد من تسجيل الخروج من التطبيق؟',
      Icons.logout,
      'تسجيل الخروج',
    );

    if (confirmed == true) {
      await DatabaseService().setCurrentUser(null);
      if (!mounted) return;

      Navigator.of(context).pushAndRemoveUntil(
        PageRouteBuilder(
          pageBuilder:
              (
                BuildContext context,
                Animation<double> animation,
                Animation<double> secondaryAnimation,
              ) => const FirebaseAuthPage(),
          transitionsBuilder:
              (
                BuildContext context,
                Animation<double> animation,
                Animation<double> secondaryAnimation,
                Widget child,
              ) {
                return FadeTransition(opacity: animation, child: child);
              },
          transitionDuration: const Duration(milliseconds: 500),
        ),
        (route) => false,
      );
    }
  }

  Future<void> _navigateToMessageTemplates() async {
    await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const MessageTemplatesPage()),
    );
  }

  void _navigateToBulkImport() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (_) => const BulkImportPage()));
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: Theme.of(dialogContext).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: Theme.of(dialogContext).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool?> _showConfirmationDialog(
    String title,
    String message,
    IconData icon,
    String confirmText, {
    bool isDestructive = false,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(
              icon,
              color: isDestructive
                  ? Theme.of(dialogContext).colorScheme.error
                  : Theme.of(dialogContext).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: isDestructive
                  ? Theme.of(dialogContext).colorScheme.error
                  : Theme.of(dialogContext).colorScheme.primary,
              foregroundColor: isDestructive
                  ? Theme.of(dialogContext).colorScheme.onError
                  : Theme.of(dialogContext).colorScheme.onPrimary,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile({
    required String title,
    required IconData icon,
    required VoidCallback? onTap,
    Color? color,
    String? subtitle,
    Widget? trailing, // Add optional trailing widget
  }) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing:
          trailing ??
          const Icon(
            Icons.arrow_forward_ios,
            size: 16,
          ), // Use provided trailing or default
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return const Divider(height: 1, indent: 70);
  }

  void _showSuccessDialog(String title, String message, IconData icon) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(icon, color: Colors.green),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('الإعدادات')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // User Profile Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.onPrimary.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _currentUser?.fullName ?? 'المستخدم',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.onPrimary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _currentUser?.roleDisplayName ?? '',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Data Management Section
            Text(
              'إدارة البيانات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),

            _buildSettingsCard([
              _buildSettingsTile(
                icon: Icons.message,
                title: 'إدارة قوالب الرسائل',
                subtitle: 'تخصيص قوالب الرسائل والإشعارات',
                onTap: _navigateToMessageTemplates,
                color: Colors.green,
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.backup,
                title: 'إدارة النسخ الاحتياطي والاستعادة',
                subtitle: 'إنشاء أو استعادة نسخة احتياطية من البيانات',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const BackupRestorePage(),
                    ),
                  );
                },
                color: Theme.of(context).colorScheme.secondary,
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.upload_file,
                title: 'استيراد مشتركين من ملف إكسل',
                subtitle:
                    'إضافة عدد كبير من المشتركين دفعة واحدة عبر نموذج إكسل احترافي',
                onTap: _navigateToBulkImport,
                color: Colors.blue,
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.history,
                title: 'سجل العمليات',
                subtitle: 'عرض جميع العمليات التي تمت على المشتركين',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ActivityLogPage(),
                    ),
                  );
                },
                color: Colors.deepPurple,
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.telegram,
                title: 'إعدادات إشعارات تلكرام',
                subtitle: 'ربط التطبيق مع بوت تلكرام لإرسال الإشعارات',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TelegramSettingsPage(),
                    ),
                  );
                },
                color: Colors.blue,
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.notifications_active,
                title: 'تذكير الديون المستحقة',
                subtitle: 'إرسال تذكير تلقائي للمشتركين الذين عليهم ديون',
                color: Colors.orange,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (_) => const DebtReminderPage()),
                  );
                },
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.print,
                title: 'إعدادات الطابعة',
                subtitle: 'إدارة إعدادات الطابعة الحرارية',
                color: Colors.teal,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => const PrinterSettingsPage(),
                    ),
                  );
                },
              ),
              _buildDivider(),
              _buildSettingsTile(
                title: 'إدارة SAS',
                icon: Icons.security,
                color: Theme.of(context).colorScheme.tertiary,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) =>  SasServersPage(),
                    ), // Updated navigation
                  );
                },
                subtitle: 'إدارة خوادم SAS Radius',
              ),
              _buildDivider(),
              _buildSettingsTile(
                title: 'إعدادات المايكروتك',
                icon: Icons.router,
                color: Colors.blueGrey,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => const MikrotikSettingsPage(),
                    ),
                  );
                },
                subtitle: 'إدارة إعدادات الاتصال بأجهزة المايكروتك',
              ),
              _buildDivider(),
              _buildSettingsTile(
                title: 'إعدادات VPN',
                icon: Icons.vpn_key,
                color: Colors.indigo,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (_) => const VpnSettingsPage()),
                  );
                },
                subtitle: 'إعداد VPN للوصول الآمن للأجهزة البعيدة',
              ),
              _buildDivider(),
              _buildSettingsTile(
                title: 'إعدادات العملة والدولة',
                icon: Icons.language,
                color: Colors.purple,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => const CurrencyCountrySettingsPage(),
                    ),
                  );
                },
                subtitle: 'تخصيص العملة ومفتاح الدولة للمعاملات والإشعارات',
              ),
              _buildDivider(),
              // _buildSettingsTile(
              //   title: 'إدارة القاعدة بيانات',
              //   icon: Icons.storage,
              //   color: Colors.teal,
              //   onTap: (_currentUser?.role == UserRole.admin)
              //       ? () {
              //           Navigator.of(context).push(
              //             MaterialPageRoute(builder: (_) => const DatabaseSettingsPage()),
              //           );
              //         }
              //       : null,
              //   subtitle: (_currentUser?.role == UserRole.admin)
              //       ? 'إدارة القاعدة بيانات التطبيق'
              //       : 'هذه الميزة متاحة فقط للمدير',
              // ),
              _buildDivider(),
              _buildSettingsTile(
                title: 'إدارة المستخدمين',
                icon: Icons.group,
                color: Colors.teal,
                onTap:
                    (_currentUser?.hasPermission(Permission.viewUsers) ?? false)
                    ? () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => const UsersManagementPage(),
                          ),
                        );
                      }
                    : null,
                subtitle:
                    (_currentUser?.hasPermission(Permission.viewUsers) ?? false)
                    ? 'إدارة المستخدمين وإدارة الصلاحيات'
                    : 'ليس لديك صلاحية لعرض المستخدمين',
              ),
            ]),
            const SizedBox(height: 20),

            // Subscription Information Section
            Text(
              'معلومات الاشتراك',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            _buildSettingsCard([
              _buildSettingsTile(
                icon: Icons.refresh,
                title: 'تجديد الاشتراك',
                subtitle: 'اختر باقة وفعّلها عبر واتساب',
                color: const Color.fromRGBO(76, 175, 80, 1),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => RenewSubscriptionPage(
                        deviceSubscription: _deviceSubscription!,
                      ),
                    ),
                  );
                },
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.vpn_key,
                title: 'رقم الحساب',
                subtitle: _deviceSubscription?.accountNumber ?? 'غير متاح',
                onTap: () {}, // No action for tapping
                color: Colors.blue,
                trailing: const SizedBox.shrink(), // No arrow
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.calendar_today,
                title: 'حالة الاشتراك',
                subtitle: _deviceSubscription?.statusText ?? 'غير متاح',
                onTap: () {}, // No action for tapping
                color: _deviceSubscription?.statusColor ?? Colors.grey,
                trailing: _deviceSubscription != null
                    ? Icon(
                        _deviceSubscription!.isValid
                            ? Icons.check_circle
                            : Icons.cancel,
                        color: _deviceSubscription!.statusColor,
                      )
                    : const SizedBox.shrink(),
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.date_range,
                title: 'تاريخ انتهاء الاشتراك',
                subtitle: _deviceSubscription?.formattedEndDate ?? 'غير متاح',
                onTap: () {}, // No action for tapping
                color: Colors.indigo,
                trailing:
                    _deviceSubscription != null &&
                        _deviceSubscription!.daysRemaining > 0
                    ? Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _deviceSubscription!.statusColor.withOpacity(
                            0.2,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_deviceSubscription!.daysRemaining} يوم',
                          style: TextStyle(
                            color: _deviceSubscription!.statusColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.refresh,
                title: 'تحديث معلومات الاشتراك',
                subtitle: 'فحص أحدث معلومات الاشتراك من الخادم',
                onTap: _loadSubscriptionInfo,
                color: Colors.orange,
              ),
            ]),
            const SizedBox(height: 20),

            // App Management Section
            Text(
              'إدارة التطبيق',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),

            _buildSettingsCard([
              _buildSettingsTile(
                icon: Icons.lock,
                title: 'تفعيل قفل التطبيق',
                subtitle: 'تفعيل الدخول للتطبيق عبر بصمة الإصبع أو الوجه',
                onTap: () {
                  // This onTap is just for consistency, the actual toggle is the switch
                },
                color: Colors.blueGrey,
                trailing: Switch(
                  value: _isBiometricEnabled,
                  onChanged: _toggleBiometricAuth,
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.restart_alt,
                title: 'إعادة ضبط التطبيق',
                subtitle:
                    'حذف جميع البيانات وإعادة التطبيق للإعدادات الافتراضية',
                onTap: (_currentUser?.role == UserRole.admin)
                    ? _resetApp
                    : null,
                color: Colors.orange,
              ),
            ]),
            const SizedBox(height: 20),

            // Account Section
            Text(
              'الحساب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),

            _buildSettingsCard([
              _buildSettingsTile(
                icon: Icons.person,
                title: 'الملف الشخصي',
                subtitle: 'عرض وتعديل معلومات الحساب الشخصية',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ProfilePage(),
                    ),
                  );
                },
                color: Theme.of(context).colorScheme.primary,
              ),
              _buildDivider(),
              _buildSettingsTile(
                icon: Icons.logout,
                title: 'تسجيل الخروج',
                subtitle: 'الخروج من التطبيق والعودة لشاشة تسجيل الدخول',
                onTap: _logout,
                color: Theme.of(context).colorScheme.error,
              ),
            ]),
            const SizedBox(height: 32),

            // App Info Section
            Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.wifi,
                      size: 32,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'نظام إدارة مشتركي الإنترنت',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'الإصدار 1.0.4',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
