import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/firebase_auth_service.dart';
import '../services/app_settings_service.dart';
import '../models/package_model.dart';
import '../models/user_model.dart';
import '../widgets/currency_country_widgets.dart';

class PackagesPage extends StatefulWidget {
  const PackagesPage({super.key});

  @override
  State<PackagesPage> createState() => _PackagesPageState();
}

final user = FirebaseAuth.instance.currentUser;

class _PackagesPageState extends State<PackagesPage>
    with TickerProviderStateMixin {
  List<PackageModel> _packages = [];
  UserModel? _currentUser;
  bool _isLoading = true;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _loadData();
    _loadCurrencySymbol();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final authService = FirebaseAuthService();

      // تحميل بيانات المستخدم من Firebase
      final firebaseUser = authService.currentUser;
      UserModel? currentUser;
      if (firebaseUser != null) {
        currentUser = await authService.getUserData(firebaseUser.uid);
      }
      await DatabaseService().syncPackagesToFirebase();
      var packages = await DatabaseService().getPackagesFire();
      if (packages.isEmpty) {
        packages = await DatabaseService().getPackages();
      }

      setState(() {
        _currentUser = currentUser;
        _packages = packages;
        _isLoading = false;
      });

      _fadeController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _showAddPackageDialog() async {
    // التحقق من الصلاحيات
    if (!(_currentUser?.hasPermission(Permission.addPackages) ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('ليس لديك صلاحية لإضافة باقات'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    await showDialog(
      context: context,
      builder: (context) => _PackageDialog(onPackageSaved: _loadData),
    );
  }

  Future<void> _showEditPackageDialog(PackageModel package) async {
    // التحقق من الصلاحيات
    if (!(_currentUser?.hasPermission(Permission.editPackages) ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('ليس لديك صلاحية لتعديل الباقات'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    await showDialog(
      context: context,
      builder: (context) =>
          _PackageDialog(package: package, onPackageSaved: _loadData),
    );
  }

  Future<void> _deletePackage(PackageModel package) async {
    // التحقق من الصلاحيات
    if (!(_currentUser?.hasPermission(Permission.deletePackages) ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('ليس لديك صلاحية لحذف الباقات'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الباقة'),
        content: Text('هل أنت متأكد من حذف باقة "${package.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DatabaseService().deletePackage(package.id);
        await _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم حذف الباقة بنجاح'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الباقة: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadCurrencySymbol() async {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة الباقات',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).colorScheme.primary,
            ),
            onPressed: _loadData,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.05),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: _isLoading
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تحميل الباقات...',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              )
            : _packages.isEmpty
            ? _buildEmptyState()
            : FadeTransition(
                opacity: _fadeAnimation,
                child: RefreshIndicator(
                  onRefresh: _loadData,
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // Responsive grid layout
                      int crossAxisCount = 2;
                      double childAspectRatio = 0.75;

                      if (constraints.maxWidth > 1200) {
                        crossAxisCount = 4;
                        childAspectRatio = 0.8;
                      } else if (constraints.maxWidth > 800) {
                        crossAxisCount = 3;
                        childAspectRatio = 0.78;
                      } else if (constraints.maxWidth < 600) {
                        crossAxisCount = 1;
                        childAspectRatio =
                            0.9; // Adjusted for more vertical space
                      }

                      return GridView.builder(
                        padding: const EdgeInsets.all(20),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: crossAxisCount,
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 20,
                          childAspectRatio: childAspectRatio,
                        ),
                        itemCount: _packages.length,
                        itemBuilder: (context, index) {
                          final package = _packages[index];
                          return AnimatedContainer(
                            duration: Duration(
                              milliseconds: 300 + (index * 100),
                            ),
                            curve: Curves.easeOutBack,
                            child: _buildPackageCard(package),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
      ),
      floatingActionButton: ElevatedButton.icon(
        onPressed:
            (_currentUser?.hasPermission(Permission.addPackages) ?? false)
            ? _showAddPackageDialog
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 2,
        ),
        icon: const Icon(Icons.add_rounded, size: 20),
        label: const Text(
          'إضافة باقة جديدة',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.inventory_2_outlined,
                size: 64,
                color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد باقات بعد',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ابدأ بإضافة أول باقة إنترنت لعملائك\nلتتمكن من إدارة الاشتراكات بسهولة',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _showAddPackageDialog,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 2,
              ),
              icon: const Icon(Icons.add_rounded, size: 20),
              label: const Text(
                'إضافة باقة جديدة',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageCard(PackageModel package) {
    final colors = [
      Theme.of(context).colorScheme.primary,
      Theme.of(context).colorScheme.secondary,
      Theme.of(context).colorScheme.tertiary,
    ];
    final colorIndex = _packages.indexOf(package) % colors.length;
    final cardColor = colors[colorIndex];

    return Card(
      elevation: 8,
      shadowColor: cardColor.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            stops: const [0.0, 0.3, 1.0],
            colors: [
              cardColor.withOpacity(0.15),
              cardColor.withOpacity(0.08),
              cardColor.withOpacity(0.05),
            ],
          ),
          border: Border.all(color: cardColor.withOpacity(0.2), width: 1.5),
        ),
        child: Stack(
          children: [
            // Background decoration pattern
            Positioned(
              top: -20,
              right: -20,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: cardColor.withOpacity(0.1),
                ),
              ),
            ),
            Positioned(
              bottom: -30,
              left: -30,
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: cardColor.withOpacity(0.05),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.all(16), // Reduced padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with icon and menu
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10), // Reduced padding
                        decoration: BoxDecoration(
                          color: cardColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(
                            14,
                          ), // Slightly smaller radius
                          boxShadow: [
                            BoxShadow(
                              color: cardColor.withOpacity(0.2),
                              blurRadius: 6, // Slightly less blur
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.wifi,
                          color: cardColor,
                          size: 22, // Slightly smaller icon
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).colorScheme.surface.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(
                            10,
                          ), // Slightly smaller radius
                        ),
                        child: PopupMenuButton<String>(
                          onSelected: (value) {
                            if (value == 'edit') {
                              _showEditPackageDialog(package);
                            } else if (value == 'delete') {
                              _deletePackage(package);
                            }
                          },
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              10,
                            ), // Slightly smaller radius
                          ),
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.edit_outlined,
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('تعديل'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.delete_outline,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('حذف'),
                                ],
                              ),
                            ),
                          ],
                          child: Padding(
                            padding: const EdgeInsets.all(6), // Reduced padding
                            child: Icon(
                              Icons.more_vert,
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withOpacity(0.7),
                              size: 18, // Slightly smaller icon
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16), // Reduced height
                  // Package name with enhanced styling
                  Text(
                    package.name,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: cardColor,
                      height: 1.2,
                      fontSize: 20, // Slightly smaller font
                    ),
                    maxLines: 1, // Reduced max lines
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 6), // Reduced height
                  // Price with currency and enhanced design
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ), // Reduced padding
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(
                        18,
                      ), // Slightly smaller radius
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CurrencyText(
                          amount: package.sellingPrice ?? package.price,
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: cardColor,
                                fontSize: 18, // Slightly smaller font
                              ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 6), // Reduced height
                  // Duration with icon
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 14, // Slightly smaller icon
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        package.durationDisplayText,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withOpacity(0.7),
                          fontSize: 12, // Slightly smaller font
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),

                  // Enhanced details section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12), // Reduced padding
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.surface.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(
                        14,
                      ), // Slightly smaller radius
                      border: Border.all(
                        color: cardColor.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        _buildEnhancedPackageDetail(
                          Icons.speed,
                          'السرعة',
                          package.speed,
                          'ميجا',
                          cardColor,
                        ),
                        const SizedBox(height: 8), // Reduced height
                        _buildEnhancedPackageDetail(
                          Icons.devices,
                          'الأجهزة',
                          '${package.deviceCount}',
                          'جهاز',
                          cardColor,
                        ),
                      ],
                    ),
                  ),

                  // Notes section with improved design
                  if (package.notes.isNotEmpty) ...[
                    const SizedBox(height: 8), // Reduced height
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(10), // Reduced padding
                      decoration: BoxDecoration(
                        color: cardColor.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(
                          10,
                        ), // Slightly smaller radius
                        border: Border.all(
                          color: cardColor.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 14, // Slightly smaller icon
                            color: cardColor.withOpacity(0.8),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              package.notes,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurface.withOpacity(0.8),
                                    height: 1.3,
                                    fontSize: 10, // Slightly smaller font
                                  ),
                              maxLines: 1, // Reduced max lines
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPackageDetail(
    IconData icon,
    String label,
    String value,
    String unit,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 18, color: color),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.6),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    unit,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _PackageDialog extends StatefulWidget {
  final PackageModel? package;
  final VoidCallback onPackageSaved;

  const _PackageDialog({this.package, required this.onPackageSaved});

  @override
  State<_PackageDialog> createState() => _PackageDialogState();
}

class _PackageDialogState extends State<_PackageDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _speedController = TextEditingController();
  final _deviceCountController = TextEditingController();
  final _notesController = TextEditingController();
  int _selectedDuration = 30;
  bool _isLoading = false;
  String _currencySymbol = 'د.ع'; // Add currency symbol field

  final List<int> _durationOptions = [7, 30, 90, 365];

  @override
  void initState() {
    super.initState();
    _loadCurrencySymbol(); // Load currency symbol
    if (widget.package != null) {
      _nameController.text = widget.package!.name;
      _priceController.text = widget.package!.price.toString();
      _sellingPriceController.text =
          widget.package?.sellingPrice?.toString() ?? '';
      _speedController.text = widget.package!.speed;
      _deviceCountController.text = widget.package!.deviceCount.toString();
      _notesController.text = widget.package!.notes;
      _selectedDuration = widget.package!.durationInDays;
    } else {
      _deviceCountController.text = '1';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _sellingPriceController.dispose();
    _speedController.dispose();
    _deviceCountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  String _getDurationText(int days) {
    switch (days) {
      case 7:
        return '1 أسبوع';
      case 30:
        return '1 شهر';
      case 90:
        return '3 أشهر';
      case 365:
        return '1 سنة';
      default:
        return '$days يوم';
    }
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> _savePackage() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final now = DateTime.now();

      if (widget.package != null) {
        // Update existing package
        final updatedPackage = widget.package!.copyWith(
          adminId: user!.uid,
          name: _nameController.text.trim(),
          price: double.parse(_priceController.text),
          sellingPrice: _sellingPriceController.text.isNotEmpty
              ? double.tryParse(_sellingPriceController.text)
              : null,
          durationInDays: _selectedDuration,
          speed: _speedController.text.trim(),
          deviceCount: int.parse(_deviceCountController.text),
          notes: _notesController.text.trim(),
        );

        await DatabaseService().updatePackage(updatedPackage);
      } else {
        // Create new package
        final package = PackageModel(
          adminId: user!.uid,
          id: _firestore.collection('packages').doc().id,
          name: _nameController.text.trim(),
          serverId: _firestore.collection('packages').doc().id,
          price: double.parse(_priceController.text),
          sellingPrice: _sellingPriceController.text.isNotEmpty
              ? double.tryParse(_sellingPriceController.text)
              : null,
          durationInDays: _selectedDuration,
          speed: _speedController.text.trim(),
          deviceCount: int.parse(_deviceCountController.text),
          notes: _notesController.text.trim(),
          createdAt: now,
        );

        widget.package != null
            ? await DatabaseService().updatePackage(package)
            : await DatabaseService().addPackage(package);
      }

      widget.onPackageSaved();
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.package != null
                  ? 'تم تحديث الباقة بنجاح'
                  : 'تم إضافة الباقة بنجاح',
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الباقة: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await AppSettingsService.getCurrencySymbol();
    setState(() {
      _currencySymbol = symbol;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        padding: const EdgeInsets.all(20), // Reduced padding
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Icon(
                      widget.package != null ? Icons.edit : Icons.add,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        widget.package != null
                            ? 'تعديل الباقة'
                            : 'إضافة باقة جديدة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        overflow:
                            TextOverflow.ellipsis, // Add ellipsis for long text
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                // اسم الباقة
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم الباقة',
                    hintText: 'مثال: باقة منزلية 50 ميجا',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: Icon(
                      Icons.inventory,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم الباقة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // السعر والمدة
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _priceController,
                        decoration: InputDecoration(
                          labelText: 'سعر الشراء', // Shortened label
                          hintText: 'مثال: 20',
                          suffixText: _currencySymbol,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: Icon(
                            Icons.attach_money,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          isDense: true, // Make it more compact
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال السعر';
                          }
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح للسعر';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 8), // Reduced width
                    Expanded(
                      child: TextFormField(
                        controller: _sellingPriceController,
                        decoration: InputDecoration(
                          labelText: 'سعر البيع',
                          hintText: 'مثال: 25',
                          suffixText: _currencySymbol,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: Icon(
                            Icons.point_of_sale,
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null &&
                              value.trim().isNotEmpty &&
                              double.tryParse(value) == null) {
                            return 'رقم غير صالح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        value: _selectedDuration,
                        decoration: InputDecoration(
                          labelText: 'المدة', // Shortened label
                          hintText: 'اختر المدة', // More concise hint
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          // Removed prefixIcon
                          isDense: true, // Make it more compact
                        ),
                        items: _durationOptions.map((duration) {
                          return DropdownMenuItem(
                            value: duration,
                            child: Text(_getDurationText(duration)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedDuration = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // السرعة وعدد الأجهزة
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _speedController,
                        decoration: InputDecoration(
                          labelText: 'السرعة',
                          hintText: 'مثال: 50 ميجا',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: Icon(
                            Icons.speed,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          isDense: true, // Make it more compact
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال السرعة';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 8), // Reduced width
                    Expanded(
                      child: TextFormField(
                        controller: _deviceCountController,
                        decoration: InputDecoration(
                          labelText: 'عدد الأجهزة',
                          hintText: 'مثال: 4',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: Icon(
                            Icons.devices,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          isDense: true, // Make it more compact
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال عدد الأجهزة';
                          }
                          if (int.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح لعدد الأجهزة';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // ملاحظات (اختياري)
                TextFormField(
                  controller: _notesController,
                  decoration: InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    hintText:
                        'مثال: مناسبة للاستخدام المنزلي، تتضمن دعم فني...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: Icon(
                      Icons.note,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 24),

                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _savePackage,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(
                            context,
                          ).colorScheme.primary,
                          foregroundColor: Theme.of(
                            context,
                          ).colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(widget.package != null ? 'تحديث' : 'إضافة'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
