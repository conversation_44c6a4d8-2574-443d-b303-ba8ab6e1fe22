import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zerotier_sockets/zerotier_sockets.dart';
import 'package:path_provider/path_provider.dart';
import '../models/vpn_settings_model.dart';

class VpnService {
  static const String _settingsKey = 'vpn_settings';
  static VpnSettings? _cachedSettings;
  static Timer? _statusTimer;
  static StreamController<VpnSettings>? _settingsController;
  static ZeroTierNode? _node;
  static final user = FirebaseAuth.instance.currentUser;
  // Stream for real-time settings updates
  static Stream<VpnSettings> get settingsStream {
    _settingsController ??= StreamController<VpnSettings>.broadcast();
    return _settingsController!.stream;
  }

  // Get current VPN settings
  static Future<VpnSettings> getSettings() async {
    if (_cachedSettings != null) {
      return _cachedSettings!;
    }

    final prefs = await SharedPreferences.getInstance();
    // final settingsJson = prefs.getString(_settingsKey);
    final settingsJson = await FirebaseFirestore.instance
        .collection('vpn_setting')
        .where('userId', isEqualTo: user!.uid)
        .get();
    if (settingsJson != null) {
      try {
        // final settingsMap = json.decode(settingsJson.docs) as Map<String, dynamic>;
        _cachedSettings = VpnSettings.fromMap(settingsJson.docs.first.data());
        return _cachedSettings!;
      } catch (e) {
        debugPrint('Error parsing VPN settings: $e');
        _cachedSettings = VpnSettings.defaultSettings();
        await saveSettings(_cachedSettings!);
        return _cachedSettings!;
      }
    }

    _cachedSettings = VpnSettings.defaultSettings();
    await saveSettings(_cachedSettings!);
    return _cachedSettings!;
  }

  // Save VPN settings
  static Future<void> saveSettings(VpnSettings settings) async {
    // final prefs = await SharedPreferences.getInstance();
    // final settingsJson = json.encode(settings.toMap());
    // await prefs.setString(_settingsKey, settingsJson);
    _cachedSettings = settings;
    _settingsController?.add(settings);
    await FirebaseFirestore.instance
        .collection('vpn_setting')
        .doc(FirebaseFirestore.instance.collection('vpn_setting').doc().id)
        .set(settings.toMap());
  }

  // Initialize ZeroTier node
  static Future<void> initializeNode() async {
    try {
      if (_node != null) {
        debugPrint('ZeroTier node already initialized');
        return; // Already initialized
      }

      debugPrint('Initializing ZeroTier node...');
      _node = ZeroTierNode.instance;

      // Get storage path
      final appDir = await _getStoragePath();
      debugPrint('ZeroTier storage path: $appDir');

      final result = _node!.initSetPath(appDir);
      debugPrint(
        'InitSetPath result: success=${result.success}, error=${result.errorMessage}',
      );

      if (!result.success) {
        throw Exception('فشل في تهيئة مسار التخزين: ${result.errorMessage}');
      }

      // Start the node
      debugPrint('Starting ZeroTier node...');
      final startResult = _node!.start();
      debugPrint(
        'Start result: success=${startResult.success}, error=${startResult.errorMessage}',
      );

      if (!startResult.success) {
        throw Exception(
          'فشل في بدء عقدة ZeroTier: ${startResult.errorMessage}',
        );
      }

      // Wait for node to come online
      debugPrint('Waiting for node to come online...');
      final onlineResult = await _node!.waitForOnline(10000);
      debugPrint(
        'Online result: success=${onlineResult.success}, error=${onlineResult.errorMessage}',
      );

      if (!onlineResult.success) {
        throw Exception('فشل في الاتصال بالشبكة: ${onlineResult.errorMessage}');
      }

      debugPrint('ZeroTier node initialized successfully');
    } catch (e) {
      debugPrint('Error initializing ZeroTier node: $e');
      throw Exception('فشل في تهيئة عقدة ZeroTier: $e');
    }
  }

  // Get storage path for ZeroTier
  static Future<String> _getStoragePath() async {
    try {
      if (Platform.isAndroid) {
        // Use app documents directory for Android
        final appDir = await getApplicationDocumentsDirectory();
        final zerotierDir = Directory('${appDir.path}/zerotier');
        if (!await zerotierDir.exists()) {
          await zerotierDir.create(recursive: true);
        }
        return zerotierDir.path;
      } else if (Platform.isIOS) {
        // Use app documents directory for iOS
        final appDir = await getApplicationDocumentsDirectory();
        final zerotierDir = Directory('${appDir.path}/zerotier');
        if (!await zerotierDir.exists()) {
          await zerotierDir.create(recursive: true);
        }
        return zerotierDir.path;
      } else {
        // Use current directory for other platforms
        final appDir = Directory('${Directory.current.path}/zerotier');
        if (!await appDir.exists()) {
          await appDir.create(recursive: true);
        }
        return appDir.path;
      }
    } catch (e) {
      debugPrint('Error getting storage path: $e');
      // Fallback to temporary directory
      final tempDir = await getTemporaryDirectory();
      final zerotierDir = Directory('${tempDir.path}/zerotier');
      if (!await zerotierDir.exists()) {
        await zerotierDir.create(recursive: true);
      }
      return zerotierDir.path;
    }
  }

  // Start VPN connection
  static Future<bool> startVpn() async {
    try {
      final settings = await getSettings();

      if (!settings.hasNetwork) {
        throw Exception('لم يتم تحديد شبكة ZeroTier');
      }

      await initializeNode();

      // Update status to connecting
      await saveSettings(
        settings.copyWith(
          connectionStatus: 'connecting',
          isEnabled: true,
          userId: user!.uid,
        ),
      );

      // Parse network ID
      final networkId = BigInt.parse(settings.networkId!, radix: 16);
      debugPrint('Joining network: ${settings.networkId!}');

      // Join the network
      final joinResult = _node!.join(networkId);
      debugPrint(
        'Join result: success=${joinResult.success}, error=${joinResult.errorMessage}',
      );

      if (!joinResult.success) {
        throw Exception('فشل في الانضمام للشبكة: ${joinResult.errorMessage}');
      }

      // Wait for network to be ready
      debugPrint('Waiting for network to be ready...');
      final readyResult = await _node!.waitForNetworkReady(networkId, 60000);
      debugPrint(
        'Network ready result: success=${readyResult.success}, error=${readyResult.errorMessage}',
      );

      if (!readyResult.success) {
        // Check if the network is actually ready despite the timeout
        final networkInfo = _node!.getNetworkInfo(networkId);
        if (networkInfo != null && networkInfo.status == NetworkStatus.ok) {
          debugPrint('Network is actually ready despite timeout');
        } else {
          throw Exception(
            'فشل في انتظار جاهزية الشبكة: ${readyResult.errorMessage}. قد تحتاج إلى موافقة من مدير الشبكة.',
          );
        }
      }

      // Wait for address assignment
      debugPrint('Waiting for address assignment...');
      final addressResult = await _node!.waitForAddressAssignment(
        networkId,
        60000,
      );
      debugPrint(
        'Address assignment result: success=${addressResult.success}, error=${addressResult.errorMessage}',
      );

      if (!addressResult.success) {
        // Check if we actually got an address despite the timeout
        final addressData = _node!.getAddress(networkId);
        if (addressData.success && addressData.data != null) {
          debugPrint('Address is actually assigned despite timeout');
        } else {
          throw Exception(
            'فشل في انتظار تعيين العنوان: ${addressResult.errorMessage}. قد تحتاج إلى موافقة من مدير الشبكة.',
          );
        }
      }

      // Get node identity
      final identityResult = _node!.getId();
      String? nodeIdentity;
      if (identityResult.success) {
        nodeIdentity = identityResult.data.toRadixString(16).padLeft(10, '0');
      }

      // Get assigned IP address
      final addressData = _node!.getAddress(networkId);
      String? assignedIp;
      if (addressData.success) {
        assignedIp = addressData.data;
      }

      // Update settings
      await saveSettings(
        settings.copyWith(
          connectionStatus: 'connected',
          nodeIdentity: nodeIdentity,
          assignedIp: assignedIp,
          lastConnected: DateTime.now(),
          isEnabled: true,
          userId: user!.uid,
        ),
      );

      // Start status monitoring
      _startStatusMonitoring();

      debugPrint('VPN connected successfully');
      return true;
    } catch (e) {
      debugPrint('Error starting VPN: $e');
      final settings = await getSettings();
      await saveSettings(
        settings.copyWith(
          connectionStatus: 'error',
          isEnabled: false,
          userId: user!.uid,
        ),
      );
      return false;
    }
  }

  // Stop VPN connection
  static Future<bool> stopVpn() async {
    try {
      final settings = await getSettings();

      if (settings.hasNetwork && _node != null) {
        final networkId = BigInt.parse(settings.networkId!, radix: 16);
        _node!.leave(networkId);
      }

      _stopStatusMonitoring();

      await saveSettings(
        settings.copyWith(
          connectionStatus: 'disconnected',
          isEnabled: false,
          userId: user!.uid,
        ),
      );

      debugPrint('VPN stopped successfully');
      return true;
    } catch (e) {
      debugPrint('Error stopping VPN: $e');
      return false;
    }
  }

  // Update network configuration
  static Future<bool> updateNetwork({
    required String networkId,
    String? networkName,
  }) async {
    try {
      final settings = await getSettings();

      // If currently connected, disconnect first
      if (settings.isConnected) {
        await stopVpn();
      }

      // Update network settings
      final updatedSettings = settings.copyWith(
        networkId: networkId,
        networkName: networkName,
        connectionStatus: 'disconnected',
        userId: user!.uid,
      );

      await saveSettings(updatedSettings);

      // Auto-connect if enabled
      if (updatedSettings.autoConnect) {
        await startVpn();
      }

      return true;
    } catch (e) {
      debugPrint('Error updating network: $e');
      return false;
    }
  }

  // Get current connection status
  static Future<Map<String, dynamic>> getConnectionStatus() async {
    try {
      final settings = await getSettings();

      if (_node == null || !settings.hasNetwork) {
        return {
          'running': false,
          'online': false,
          'identity': null,
          'networks': [],
        };
      }

      final networkId = BigInt.parse(settings.networkId!, radix: 16);
      final networkInfo = _node!.getNetworkInfo(networkId);
      final identityResult = _node!.getId();

      String? nodeIdentity;
      if (identityResult.success) {
        nodeIdentity = identityResult.data.toRadixString(16).padLeft(10, '0');
      }

      bool isConnected = false;
      if (networkInfo != null) {
        isConnected = networkInfo.status == NetworkStatus.ok;
      }

      return {
        'running': _node!.running,
        'online': _node!.online && isConnected,
        'identity': nodeIdentity,
        'networks': networkInfo != null ? [networkInfo] : [],
      };
    } catch (e) {
      debugPrint('Error getting connection status: $e');
      return {
        'running': false,
        'online': false,
        'identity': null,
        'networks': [],
      };
    }
  }

  // Start status monitoring
  static void _startStatusMonitoring() {
    _stopStatusMonitoring();
    _statusTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      try {
        final settings = await getSettings();
        final status = await getConnectionStatus();

        String newStatus = 'disconnected';
        if (status['running'] == true) {
          if (status['online'] == true) {
            newStatus = 'connected';
          } else {
            newStatus = 'connecting';
          }
        }

        if (newStatus != settings.connectionStatus) {
          await saveSettings(
            settings.copyWith(
              connectionStatus: newStatus,
              lastConnected: newStatus == 'connected'
                  ? DateTime.now()
                  : settings.lastConnected,
              userId: user!.uid,
            ),
          );
        }
      } catch (e) {
        debugPrint('Error in status monitoring: $e');
      }
    });
  }

  // Stop status monitoring
  static void _stopStatusMonitoring() {
    _statusTimer?.cancel();
    _statusTimer = null;
  }

  // Validate network ID format
  static bool isValidNetworkId(String networkId) {
    try {
      if (networkId.length != 16) return false;
      BigInt.parse(networkId, radix: 16);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Test network connectivity
  static Future<bool> testNetworkConnectivity(String networkId) async {
    try {
      if (!isValidNetworkId(networkId)) {
        return false;
      }

      await initializeNode();
      final networkIdBigInt = BigInt.parse(networkId, radix: 16);

      final joinResult = _node!.join(networkIdBigInt);
      if (!joinResult.success) {
        return false;
      }

      // Wait for network to be ready
      final readyResult = await _node!.waitForNetworkReady(
        networkIdBigInt,
        15000,
      );
      if (!readyResult.success) {
        _node!.leave(networkIdBigInt);
        return false;
      }

      // Check if we got an address
      final addressResult = await _node!.waitForAddressAssignment(
        networkIdBigInt,
        15000,
      );
      final hasAddress = addressResult.success;

      _node!.leave(networkIdBigInt);
      return hasAddress;
    } catch (e) {
      debugPrint('Error testing network connectivity: $e');
      return false;
    }
  }

  // Check if network requires approval
  static Future<Map<String, dynamic>> checkNetworkApprovalStatus(
    String networkId,
  ) async {
    try {
      if (_node == null) {
        return {'success': false, 'error': 'Node not initialized'};
      }

      final networkIdBigInt = BigInt.parse(networkId, radix: 16);
      final networkInfo = _node!.getNetworkInfo(networkIdBigInt);

      if (networkInfo == null) {
        return {'success': false, 'error': 'Network not found or not joined'};
      }

      String status = 'unknown';
      String message = '';
      bool requiresApproval = false;

      switch (networkInfo.status) {
        case NetworkStatus.waitingForConfig:
          status = 'waiting_for_config';
          message = 'في انتظار تكوين الشبكة...';
          break;
        case NetworkStatus.ok:
          status = 'approved';
          message = 'الشبكة مفعلة ومتاحة';
          break;
        case NetworkStatus.accessDenied:
          status = 'access_denied';
          message = 'تم رفض الوصول للشبكة';
          requiresApproval = true;
          break;
        case NetworkStatus.notFound:
          status = 'not_found';
          message = 'الشبكة غير موجودة';
          break;
        case NetworkStatus.portError:
          status = 'port_error';
          message = 'خطأ في المنفذ';
          break;
        case NetworkStatus.clientTooOld:
          status = 'client_too_old';
          message = 'إصدار العميل قديم جداً';
          break;
      }

      return {
        'success': true,
        'status': status,
        'message': message,
        'requiresApproval': requiresApproval,
        'networkName': networkInfo.name,
        'transportReady': networkInfo.transportIsReady,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Get detailed network status information
  static Future<Map<String, dynamic>> getNetworkStatusInfo(
    String networkId,
  ) async {
    try {
      if (_node == null) {
        return {'success': false, 'error': 'Node not initialized'};
      }

      final networkIdBigInt = BigInt.parse(networkId, radix: 16);
      final networkInfo = _node!.getNetworkInfo(networkIdBigInt);

      if (networkInfo == null) {
        return {'success': false, 'error': 'Network not found or not joined'};
      }

      return {
        'success': true,
        'networkId': networkId,
        'status': networkInfo.status.toString(),
        'name': networkInfo.name,
        'mac': networkInfo.mac.toString(),
        'broadcast': networkInfo.broadcastEnabled,
        'mtu': networkInfo.mtu,
        'transportReady': networkInfo.transportIsReady,
        'nodeRunning': _node!.running,
        'nodeOnline': _node!.online,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Test ZeroTier functionality
  static Future<Map<String, dynamic>> testZeroTierFunctionality() async {
    try {
      debugPrint('=== Testing ZeroTier Functionality ===');

      // Test 1: Initialize node
      await initializeNode();
      debugPrint('✓ Node initialization successful');

      // Test 2: Get node ID
      final identityResult = _node!.getId();
      if (identityResult.success) {
        final nodeId = identityResult.data.toRadixString(16).padLeft(10, '0');
        debugPrint('✓ Node ID: $nodeId');
      } else {
        debugPrint('✗ Failed to get node ID: ${identityResult.errorMessage}');
      }

      // Test 3: Check if node is running
      final isRunning = _node!.running;
      debugPrint('✓ Node running: $isRunning');

      // Test 4: Check if node is online
      final isOnline = _node!.online;
      debugPrint('✓ Node online: $isOnline');

      return {
        'success': true,
        'nodeId': identityResult.success
            ? identityResult.data.toRadixString(16).padLeft(10, '0')
            : null,
        'running': isRunning,
        'online': isOnline,
        'message': 'ZeroTier functionality test completed successfully',
      };
    } catch (e) {
      debugPrint('✗ ZeroTier functionality test failed: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'ZeroTier functionality test failed',
      };
    }
  }

  // Dispose resources
  static void dispose() {
    _stopStatusMonitoring();
    _settingsController?.close();
    _settingsController = null;
    _cachedSettings = null;

    if (_node != null) {
      _node!.stop();
      _node = null;
    }
  }
}
