import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:isp_manager/services/database_service.dart';
import 'package:router_os_client/router_os_client.dart'; // Assuming RouterOSClient is the new name
import 'package:isp_manager/models/mikrotik_device_model.dart';
import 'package:isp_manager/services/sqlite_service.dart';
import 'package:flutter/foundation.dart'; // For debugPrint
import 'package:dartssh2/dartssh2.dart';
import 'dart:convert';

class MikrotikService {
  static final MikrotikService _instance = MikrotikService._internal();
  factory MikrotikService() => _instance;
  MikrotikService._internal();

  RouterOSClient? _client;
  MikrotikDeviceModel? _connectedDevice;

  ValueNotifier<bool> isConnected = ValueNotifier<bool>(false);

  Future<void> init() async {
    await _loadConnectedDevice();
    if (_connectedDevice != null) {
      await connect(_connectedDevice!);
    }
  }

  Future<void> _loadConnectedDevice() async {
    _connectedDevice = await SQLiteService().getConnectedMikrotikDevice().then((
      map,
    ) {
      if (map != null) {
        return MikrotikDeviceModel.fromMap(map);
      }
      return null;
    });
    debugPrint(
      'MikrotikService: Loaded connected device: ${_connectedDevice?.name}',
    );
  }

  Future<bool> connect(MikrotikDeviceModel device) async {
    return await testConnection(
      device.host,
      device.port,
      device.username,
      device.password,
      setAsConnected: true,
    );
  }

  Future<void> disconnect() async {
    if (_client != null) {
      _client!.close();
      _client = null;
      if (_connectedDevice != null) {
        _connectedDevice!.isConnected = false;
        await SQLiteService().updateMikrotikDevice(_connectedDevice!.toMap());
      }
      isConnected.value = false;
      debugPrint('MikroTikService: Disconnected');
    }
  }

  Future<bool> testConnection(
    String host,
    int port,
    String username,
    String password, {
    bool setAsConnected = false,
  }) async {
    RouterOSClient? testClient;
    try {
      testClient = RouterOSClient(
        address: host,
        user: username,
        password: password,
        useSsl: false,
        verbose: false,
      );

      // Test actual login
      bool loginSuccess = await testClient.login();
      if (!loginSuccess) {
        debugPrint('MikroTikService: Login failed for $host:$port');
        testClient.close();
        return false;
      }

      debugPrint(
        'MikroTikService: Successfully connected and logged in to $host:$port',
      );

      if (setAsConnected) {
        _client?.close();
        _client = testClient;
        _connectedDevice = MikrotikDeviceModel(
          adminId: DatabaseService().adminId,
          id: FirebaseFirestore.instance
              .collection('mikrotik_devices')
              .doc()
              .id,
          name: 'Temporary Device', // Name is not important for connection test
          host: host,
          port: port,
          username: username,
          password: password,
          isConnected: true,
        );
        isConnected.value = true;
      } else {
        testClient.close(); // Close test client if not setting as connected
      }
      return true;
    } catch (e) {
      debugPrint('MikroTikService: Test connection failed to $host:$port: $e');
      testClient?.close();
      if (setAsConnected) {
        isConnected.value = false;
      }
      return false;
    }
  }

  RouterOSClient? get client => _client;
  MikrotikDeviceModel? get connectedDevice => _connectedDevice;

  Future<void> setConnectedDevice(MikrotikDeviceModel device) async {
    // Disconnect from any previously connected device
    if (_connectedDevice != null && _connectedDevice!.id != device.id) {
      _connectedDevice!.isConnected = false;
      await SQLiteService().updateMikrotikDevice(_connectedDevice!.toMap());
    }

    // Set the new device as connected
    device.isConnected = true;
    await SQLiteService().updateMikrotikDevice(device.toMap());
    _connectedDevice = device;
    await connect(device); // Attempt to connect immediately
  }

  Future<void> clearConnectedDevice() async {
    if (_connectedDevice != null) {
      _connectedDevice!.isConnected = false;
      await SQLiteService().updateMikrotikDevice(_connectedDevice!.toMap());
      _connectedDevice = null;
    }
    await disconnect();
  }

  // Method to fetch comprehensive MikroTik device details
  Future<Map<String, dynamic>> getMikrotikDeviceDetails(
    MikrotikDeviceModel device,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      // Login to the device
      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        debugPrint('MikroTikService: Login failed for ${device.name}');
        return {'status': 'error', 'message': 'Login failed'};
      }

      debugPrint('MikroTikService: Successfully logged in to ${device.name}');

      Map<String, dynamic> deviceDetails = {};

      try {
        // Get system resource information
        List<Map<String, String>> systemResource = await client.talk([
          '/system/resource/print',
        ]);
        if (systemResource.isNotEmpty) {
          var resource = systemResource.first;
          deviceDetails['uptime'] = resource['uptime'] ?? 'Unknown';
          deviceDetails['version'] = resource['version'] ?? 'Unknown';
          deviceDetails['board_name'] = resource['board-name'] ?? 'Unknown';
          deviceDetails['cpu_load'] = resource['cpu-load'] ?? 'Unknown';
          deviceDetails['free_memory'] = resource['free-memory'] ?? 'Unknown';
          deviceDetails['total_memory'] = resource['total-memory'] ?? 'Unknown';
          deviceDetails['cpu'] = resource['cpu'] ?? 'Unknown';
          deviceDetails['cpu_count'] = resource['cpu-count'] ?? 'Unknown';
          deviceDetails['cpu_frequency'] =
              resource['cpu-frequency'] ?? 'Unknown';
          deviceDetails['architecture_name'] =
              resource['architecture-name'] ?? 'Unknown';
        }

        // Get system identity
        List<Map<String, String>> identity = await client.talk([
          '/system/identity/print',
        ]);
        if (identity.isNotEmpty) {
          deviceDetails['identity'] = identity.first['name'] ?? 'Unknown';
        }

        // Get interface information
        List<Map<String, String>> interfaces = await client.talk([
          '/interface/print',
        ]);
        List<Map<String, String>> interfaceDetails = [];
        for (var interface in interfaces) {
          interfaceDetails.add({
            'name': interface['name'] ?? 'Unknown',
            'type': interface['type'] ?? 'Unknown',
            'running': interface['running'] ?? 'false',
            'disabled': interface['disabled'] ?? 'false',
            'mtu': interface['mtu'] ?? 'Unknown',
          });
        }
        deviceDetails['interfaces'] = interfaceDetails;

        // Get IP addresses
        List<Map<String, String>> ipAddresses = await client.talk([
          '/ip/address/print',
        ]);
        List<Map<String, String>> addressDetails = [];
        for (var address in ipAddresses) {
          addressDetails.add({
            'address': address['address'] ?? 'Unknown',
            'interface': address['interface'] ?? 'Unknown',
            'network': address['network'] ?? 'Unknown',
            'disabled': address['disabled'] ?? 'false',
          });
        }
        deviceDetails['ip_addresses'] = addressDetails;

        // Get DHCP server information
        try {
          List<Map<String, String>> dhcpServers = await client.talk([
            '/ip/dhcp-server/print',
          ]);
          List<Map<String, String>> dhcpDetails = [];
          for (var dhcp in dhcpServers) {
            dhcpDetails.add({
              'name': dhcp['name'] ?? 'Unknown',
              'interface': dhcp['interface'] ?? 'Unknown',
              'address_pool': dhcp['address-pool'] ?? 'Unknown',
              'disabled': dhcp['disabled'] ?? 'false',
            });
          }
          deviceDetails['dhcp_servers'] = dhcpDetails;
        } catch (e) {
          debugPrint('MikroTikService: Error fetching DHCP servers: $e');
          deviceDetails['dhcp_servers'] = [];
        }

        // Get wireless information (if available)
        try {
          List<Map<String, String>> wireless = await client.talk([
            '/interface/wireless/print',
          ]);
          List<Map<String, String>> wirelessDetails = [];
          for (var wifi in wireless) {
            wirelessDetails.add({
              'name': wifi['name'] ?? 'Unknown',
              'ssid': wifi['ssid'] ?? 'Unknown',
              'mode': wifi['mode'] ?? 'Unknown',
              'frequency': wifi['frequency'] ?? 'Unknown',
              'disabled': wifi['disabled'] ?? 'false',
            });
          }
          deviceDetails['wireless_interfaces'] = wirelessDetails;
        } catch (e) {
          debugPrint('MikroTikService: Error fetching wireless interfaces: $e');
          deviceDetails['wireless_interfaces'] = [];
        }
        deviceDetails['status'] = 'success';
        deviceDetails['last_updated'] = DateTime.now().toIso8601String();

        return deviceDetails;
      } catch (e) {
        debugPrint('MikroTikService: Error executing commands: $e');
        return {
          'status': 'error',
          'message': 'Error fetching device information: $e',
        };
      }
    } catch (e) {
      debugPrint('MikroTikService: Failed to connect to ${device.name}: $e');
      return {'status': 'error', 'message': 'Connection failed: $e'};
    } finally {
      client?.close();
    }
  }

  // Method to get interface traffic statistics
  Future<Map<String, dynamic>> getInterfaceTraffic(
    MikrotikDeviceModel device,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        return {'status': 'error', 'message': 'Login failed'};
      }

      // Get interface traffic statistics
      List<Map<String, String>> interfaces = await client.talk([
        '/interface/print',
        '=stats',
      ]);
      Map<String, Map<String, dynamic>> trafficData = {};

      for (var interface in interfaces) {
        String name = interface['name'] ?? 'Unknown';
        trafficData[name] = {
          'rx_bytes': int.tryParse(interface['rx-byte'] ?? '0') ?? 0,
          'tx_bytes': int.tryParse(interface['tx-byte'] ?? '0') ?? 0,
          'rx_packets': int.tryParse(interface['rx-packet'] ?? '0') ?? 0,
          'tx_packets': int.tryParse(interface['tx-packet'] ?? '0') ?? 0,
          'rx_drops': int.tryParse(interface['rx-drop'] ?? '0') ?? 0,
          'tx_drops': int.tryParse(interface['tx-drop'] ?? '0') ?? 0,
          'rx_errors': int.tryParse(interface['rx-error'] ?? '0') ?? 0,
          'tx_errors': int.tryParse(interface['tx-error'] ?? '0') ?? 0,
          'running': interface['running'] == 'true',
          'type': interface['type'] ?? 'Unknown',
        };
      }

      return {
        'status': 'success',
        'traffic_data': trafficData,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('MikroTikService: Error fetching traffic data: $e');
      return {'status': 'error', 'message': 'Error fetching traffic data: $e'};
    } finally {
      client?.close();
    }
  }

  // Method to execute custom commands
  Future<String> executeCustomCommand(
    MikrotikDeviceModel device,
    String command,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        return 'خطأ: فشل تسجيل الدخول';
      }

      // Parse command - split by spaces but handle quoted arguments
      List<String> commandParts = _parseCommand(command);

      if (commandParts.isEmpty) {
        return 'خطأ: أمر فارغ';
      }

      // Execute the command
      List<Map<String, String>> result = await client.talk(commandParts);

      if (result.isEmpty) {
        return 'تم تنفيذ الأمر بنجاح (بدون مخرجات)';
      }

      // Format the result
      StringBuffer output = StringBuffer();
      for (int i = 0; i < result.length; i++) {
        output.writeln('النتيجة ${i + 1}:');
        result[i].forEach((key, value) {
          output.writeln('  $key: $value');
        });
        if (i < result.length - 1) output.writeln();
      }

      return output.toString();
    } catch (e) {
      debugPrint('MikroTikService: Error executing command: $e');
      return 'خطأ في تنفيذ الأمر: $e';
    } finally {
      client?.close();
    }
  }

  // Helper method to parse command string
  List<String> _parseCommand(String command) {
    List<String> parts = [];
    StringBuffer current = StringBuffer();
    bool inQuotes = false;

    for (int i = 0; i < command.length; i++) {
      String char = command[i];

      if (char == '"' && (i == 0 || command[i - 1] != '\\')) {
        inQuotes = !inQuotes;
      } else if (char == ' ' && !inQuotes) {
        if (current.isNotEmpty) {
          parts.add(current.toString());
          current.clear();
        }
      } else {
        current.write(char);
      }
    }

    if (current.isNotEmpty) {
      parts.add(current.toString());
    }

    return parts;
  }

  // Method to get system performance metrics
  Future<Map<String, dynamic>> getSystemPerformance(
    MikrotikDeviceModel device,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        return {'status': 'error', 'message': 'Login failed'};
      }

      Map<String, dynamic> performance = {};

      // Get system resource
      List<Map<String, String>> systemResource = await client.talk([
        '/system/resource/print',
      ]);
      if (systemResource.isNotEmpty) {
        var resource = systemResource.first;
        performance['cpu_load'] = resource['cpu-load'] ?? '0%';
        performance['free_memory'] = resource['free-memory'] ?? '0';
        performance['total_memory'] = resource['total-memory'] ?? '0';
        performance['uptime'] = resource['uptime'] ?? '0';
      }

      // Get interface count and traffic summary
      List<Map<String, String>> interfaces = await client.talk([
        '/interface/print',
        '=count-only',
      ]);
      performance['interface_count'] = interfaces.length;

      // Get active connections count (if available)
      try {
        List<Map<String, String>> connections = await client.talk([
          '/ip/firewall/connection/print',
          '=count-only',
        ]);
        performance['active_connections'] = connections.length;
      } catch (e) {
        performance['active_connections'] = 0;
      }

      performance['status'] = 'success';
      performance['timestamp'] = DateTime.now().toIso8601String();

      return performance;
    } catch (e) {
      debugPrint('MikroTikService: Error fetching performance metrics: $e');
      return {'status': 'error', 'message': 'Error fetching performance: $e'};
    } finally {
      client?.close();
    }
  }

  // Method to manage interface status (enable/disable)
  Future<String> setInterfaceStatus(
    MikrotikDeviceModel device,
    String interfaceName,
    bool enable,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        return 'خطأ: فشل تسجيل الدخول';
      }

      String command = enable ? '/interface/enable' : '/interface/disable';
      await client.talk([command, '=numbers=$interfaceName']);

      return enable
          ? 'تم تفعيل المنفذ $interfaceName'
          : 'تم تعطيل المنفذ $interfaceName';
    } catch (e) {
      debugPrint('MikroTikService: Error setting interface status: $e');
      return 'خطأ في تغيير حالة المنفذ: $e';
    } finally {
      client?.close();
    }
  }

  // Method to get DHCP lease information
  Future<Map<String, dynamic>> getDHCPLeases(MikrotikDeviceModel device) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        return {'status': 'error', 'message': 'Login failed'};
      }

      List<Map<String, String>> leases = await client.talk([
        '/ip/dhcp-server/lease/print',
      ]);
      List<Map<String, dynamic>> leaseDetails = [];

      for (var lease in leases) {
        leaseDetails.add({
          'address': lease['address'] ?? 'Unknown',
          'mac_address': lease['mac-address'] ?? 'Unknown',
          'client_id': lease['client-id'] ?? '',
          'server': lease['server'] ?? 'Unknown',
          'status': lease['status'] ?? 'Unknown',
          'expires_after': lease['expires-after'] ?? 'Never',
          'last_seen': lease['last-seen'] ?? 'Never',
        });
      }

      return {
        'status': 'success',
        'leases': leaseDetails,
        'total_leases': leaseDetails.length,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('MikroTikService: Error fetching DHCP leases: $e');
      return {'status': 'error', 'message': 'Error fetching DHCP leases: $e'};
    } finally {
      client?.close();
    }
  }

  // Method to reboot the MikroTik device
  Future<String> rebootMikrotik(MikrotikDeviceModel device) async {
    return await executeCustomCommand(device, '/system/reboot');
  }

  // Method to shut down the MikroTik device
  Future<String> shutdownMikrotik(MikrotikDeviceModel device) async {
    return await executeCustomCommand(device, '/system/shutdown');
  }

  Future<List<Map<String, String>>> getInterfaces(
    MikrotikDeviceModel device,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        debugPrint('MikroTikService: Login failed for ${device.name}');
        return [];
      }

      List<Map<String, String>> interfaces = await client.talk([
        '/interface/print',
      ]);
      return interfaces
          .map(
            (interface) => {
              'name': interface['name'] ?? 'Unknown',
              'type': interface['type'] ?? 'Unknown',
              'running': interface['running'] ?? 'false',
              'disabled': interface['disabled'] ?? 'false',
              'mtu': interface['mtu'] ?? 'Unknown',
            },
          )
          .toList();
    } catch (e) {
      debugPrint('MikroTikService: Error getting interfaces: $e');
      return [];
    } finally {
      client?.close();
    }
  }

  Future<List<Map<String, String>>> getWirelessInterfaces(
    MikrotikDeviceModel device,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        debugPrint('MikroTikService: Login failed for ${device.name}');
        return [];
      }

      List<Map<String, String>> wirelessInterfaces = await client.talk([
        '/interface/wireless/print',
      ]);
      return wirelessInterfaces
          .map(
            (wifi) => {
              'name': wifi['name'] ?? 'Unknown',
              'ssid': wifi['ssid'] ?? 'Unknown',
              'mode': wifi['mode'] ?? 'Unknown',
              'frequency': wifi['frequency'] ?? 'Unknown',
              'running': wifi['running'] ?? 'false',
              'disabled': wifi['disabled'] ?? 'false',
            },
          )
          .toList();
    } catch (e) {
      debugPrint('MikroTikService: Error getting wireless interfaces: $e');
      return [];
    } finally {
      client?.close();
    }
  }

  Future<List<Map<String, String>>> getDHCPServers(
    MikrotikDeviceModel device,
  ) async {
    RouterOSClient? client;
    try {
      client = RouterOSClient(
        address: device.host,
        user: device.username,
        password: device.password,
        useSsl: false,
        verbose: false,
      );

      bool loginSuccess = await client.login();
      if (!loginSuccess) {
        debugPrint('MikroTikService: Login failed for ${device.name}');
        return [];
      }

      List<Map<String, String>> dhcpServers = await client.talk([
        '/ip/dhcp-server/print',
      ]);
      return dhcpServers
          .map(
            (dhcp) => {
              'name': dhcp['name'] ?? 'Unknown',
              'interface': dhcp['interface'] ?? 'Unknown',
              'address_pool': dhcp['address-pool'] ?? 'Unknown',
              'disabled': dhcp['disabled'] ?? 'false',
            },
          )
          .toList();
    } catch (e) {
      debugPrint('MikroTikService: Error getting DHCP servers: $e');
      return [];
    } finally {
      client?.close();
    }
  }

  /// تنفيذ أمر CLI مخصص على جهاز MikroTik عبر SSH
  Future<String> runCustomSSHCommand(
    MikrotikDeviceModel device,
    String command,
  ) async {
    try {
      final socket = await SSHSocket.connect(device.host, 22);
      final client = SSHClient(
        socket,
        username: device.username,
        onPasswordRequest: () => device.password,
      );
      final result = await client.run(command);
      client.close();
      await socket.close();
      return utf8.decode(result);
    } catch (e) {
      return 'خطأ في تنفيذ الأمر عبر SSH: $e';
    }
  }
}
