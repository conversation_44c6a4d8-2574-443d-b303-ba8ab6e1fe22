import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:isp_manager/pages/settings_page.dart' show SettingsPage;
import 'package:shared_preferences/shared_preferences.dart';
import '../services/database_service.dart';
import '../services/firebase_auth_service.dart';
import '../services/ai_service.dart';
import '../services/sas_api_service.dart';
import '../services/app_settings_service.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../models/user_model.dart';
import 'subscribers_page.dart';
import 'packages_page.dart';
import 'expenses_page.dart'; // New import
import 'network_devices_page.dart'; // Add this import
import 'package:isp_manager/pages/profile_page.dart';
import 'package:isp_manager/pages/firebase_auth_page.dart';
import '../services/firebase_in_app_messaging_service.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  UserModel? _currentUser;
  List<SubscriberModel> _subscribers = [];
  List<PackageModel> _packages = [];
  bool _isLoading = true;
  String _aiInsights = '';
  double? _sasBalance;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  final FirebaseInAppMessagingService _inAppMessagingService = FirebaseInAppMessagingService();

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _loadData();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await DatabaseService().loadAdmin();
    setState(() => _isLoading = true);
    try {
      // تحميل بيانات المستخدم من Firebase
      final authService = FirebaseAuthService();
      final firebaseUser = authService.currentUser;
      UserModel? currentUser;
      if (firebaseUser != null) {
        currentUser = await authService.getUserData(firebaseUser.uid);
      }

      var subscribers = await DatabaseService().getSubscribersFire();
      if (subscribers.isEmpty) {
        subscribers = await DatabaseService().getSubscribers();
      }
      var packages = await DatabaseService().getPackagesFire();
      if (packages.isEmpty) {
        packages = await DatabaseService().getPackages();
      }
      // جلب بيانات رصيد SAS
      double? sasBalance;
      try {
        final prefs = await SharedPreferences.getInstance();
        final sasHost = prefs.getString('sas_host');
        final sasUsername = prefs.getString('sas_username');
        final sasPassword = prefs.getString('sas_password');
        if (sasHost != null && sasUsername != null && sasPassword != null) {
          final sasApiService = SasApiService();
          final loggedIn = await sasApiService.login();
          if (loggedIn) {
            sasBalance = await sasApiService.getSasBalance();
          }
        }
      } catch (_) {}

      setState(() {
        _currentUser = currentUser;
        _subscribers = subscribers;
        _packages = packages;
        _sasBalance = sasBalance;
        _isLoading = false;
      });
      _fadeController.forward();
      _loadAIInsights();
      
      // Trigger dashboard view event for in-app messaging
      await _inAppMessagingService.triggerDashboardView();
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadAIInsights() async {
    if (_subscribers.isNotEmpty && _packages.isNotEmpty) {
      final insights = await AIService().generateSubscriberInsights(
        _subscribers,
        _packages,
      );
      setState(() {
        _aiInsights = insights;
      });
    }
  }

  Future<void> _logout() async {
    final authService = FirebaseAuthService();
    await authService.signOut();
    await DatabaseService().setCurrentUser(null);
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const FirebaseAuthPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
                Theme.of(context).colorScheme.surface,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'جاري تحميل البيانات...',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // إنشاء قائمة الصفحات حسب الصلاحيات
    final List<Widget> pages = [];
    final List<BottomNavigationBarItem> navigationItems = [];

    // الصفحة الرئيسية - متاحة للجميع
    pages.add(_buildDashboard());
    navigationItems.add(
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'الرئيسية',
      ),
    );

    // صفحة المشتركين
    if (_currentUser?.hasPermission(Permission.viewSubscribers) ?? false) {
      pages.add(const SubscribersPage());
      navigationItems.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.people),
          label: 'المشتركين',
        ),
      );
    }

    // صفحة الباقات
    if (_currentUser?.hasPermission(Permission.viewPackages) ?? false) {
      pages.add(const PackagesPage());
      navigationItems.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.inventory),
          label: 'الباقات',
        ),
      );
    }

    // صفحة المصاريف
    if (_currentUser?.hasPermission(Permission.viewExpenses) ?? false) {
      pages.add(const ExpensesPage());
      navigationItems.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.monetization_on),
          label: 'المصاريف',
        ),
      );
    }

    // صفحة الأجهزة
    if (_currentUser?.hasPermission(Permission.viewNetworkDevices) ?? false) {
      pages.add(const NetworkDevicesPage());
      navigationItems.add(
        const BottomNavigationBarItem(icon: Icon(Icons.wifi), label: 'الأجهزة'),
      );
    }

    // صفحة الإعدادات
    if (_currentUser?.hasPermission(Permission.viewSettings) ?? false) {
      pages.add(const SettingsPage());
      navigationItems.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'الإعدادات',
        ),
      );
    }

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: pages.isNotEmpty
            ? pages[_selectedIndex.clamp(0, pages.length - 1)]
            : _buildDashboard(),
      ),
      bottomNavigationBar: navigationItems.isNotEmpty
          ? Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: BottomNavigationBar(
                currentIndex: _selectedIndex.clamp(
                  0,
                  navigationItems.length - 1,
                ),
                onTap: (index) {
                  if (index < pages.length) {
                    setState(() {
                      _selectedIndex = index;
                    });
                  }
                },
                type: BottomNavigationBarType.fixed,
                backgroundColor: Theme.of(context).colorScheme.surface,
                selectedItemColor: Theme.of(context).colorScheme.primary,
                unselectedItemColor: Theme.of(
                  context,
                ).colorScheme.onSurface.withOpacity(0.6),
                selectedLabelStyle: Theme.of(
                  context,
                ).textTheme.labelSmall?.copyWith(fontWeight: FontWeight.bold),
                unselectedLabelStyle: Theme.of(context).textTheme.labelSmall,
                items: navigationItems,
              ),
            )
          : null,
    );
  }

  Widget _buildDashboard() {
    final activeSubscribers = _subscribers.where((s) => !s.isExpired).length;
    final expiredSubscribers = _subscribers.where((s) => s.isExpired).length;
    final totalDebt = _subscribers.fold(0.0, (sum, s) => sum + s.debtAmount);
    final expiringToday = _subscribers.where((s) => s.isExpiringSoon).length;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'لوحة التحكم',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            icon: Icon(
              Icons.account_circle,
              color: Theme.of(context).colorScheme.primary,
            ),
            onSelected: (value) {
              if (value == 'logout') {
                _logout();
              } else if (value == 'profile') {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const ProfilePage()),
                );
              }
            },
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _currentUser?.fullName ?? '',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          _currentUser?.roleDisplayName ?? '',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withOpacity(0.6),
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              PopupMenuItem<String>(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(
                      Icons.logout,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تسجيل الخروج',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.05),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: _loadData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.primary.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.waving_hand,
                            color: Theme.of(context).colorScheme.onPrimary,
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'مرحباً، ${_currentUser?.fullName ?? 'المستخدم'}',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'نظرة سريعة على أحدث إحصائيات نظام إدارة المشتركين',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onPrimary.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Statistics Cards
                if (_sasBalance != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: FutureBuilder<String>(
                      future: AppSettingsService.getCurrencySymbol(),
                      builder: (context, snapshot) {
                        return _buildStatCard(
                          title: 'رصيد SAS',
                          value: _sasBalance!.toStringAsFixed(2),
                          icon: Icons.account_balance,
                          color: Theme.of(context).colorScheme.primary,
                          subtitle: snapshot.data ?? '',
                        );
                      },
                    ),
                  ),
                ],
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.0, // Adjusted to give more vertical space
                  children: [
                    _buildStatCard(
                      title: 'إجمالي المشتركين',
                      value: '${_subscribers.length}',
                      icon: Icons.people,
                      color: Theme.of(context).colorScheme.primary,
                      subtitle: '$activeSubscribers نشط',
                    ),
                    _buildStatCard(
                      title: 'الباقات المتاحة',
                      value: '${_packages.length}',
                      icon: Icons.inventory,
                      color: Theme.of(context).colorScheme.secondary,
                      subtitle: 'باقة مختلفة',
                    ),
                    FutureBuilder<String>(
                      future: AppSettingsService.getCurrencySymbol(),
                      builder: (context, snapshot) {
                        return _buildStatCard(
                          title: 'إجمالي الديون',
                          value: totalDebt.toStringAsFixed(0),
                          icon: Icons.account_balance_wallet,
                          color: Theme.of(context).colorScheme.tertiary,
                          subtitle: snapshot.data ?? '',
                        );
                      },
                    ),
                    _buildStatCard(
                      title: 'منتهية اليوم',
                      value: '$expiringToday',
                      icon: Icons.warning,
                      color: expiringToday > 0
                          ? Theme.of(context).colorScheme.error
                          : Colors.green,
                      subtitle: 'اشتراك',
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Chart Section
                if (_subscribers.isNotEmpty) ...[
                  Text(
                    'توزيع حالات المشتركين',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 200,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: PieChart(
                      PieChartData(
                        sections: [
                          PieChartSectionData(
                            value: activeSubscribers.toDouble(),
                            title: 'نشط\n$activeSubscribers',
                            color: Theme.of(context).colorScheme.primary,
                            radius: 60,
                            titleStyle: Theme.of(context).textTheme.labelSmall
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          PieChartSectionData(
                            value: expiredSubscribers.toDouble(),
                            title: 'منتهي\n$expiredSubscribers',
                            color: Theme.of(context).colorScheme.error,
                            radius: 60,
                            titleStyle: Theme.of(context).textTheme.labelSmall
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                        centerSpaceRadius: 40,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // AI Insights Section
                if (_aiInsights.isNotEmpty) ...[
                  Text(
                    'رؤى ذكية 🤖',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(
                            context,
                          ).colorScheme.secondary.withOpacity(0.1),
                          Theme.of(
                            context,
                          ).colorScheme.tertiary.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.secondary.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.psychology,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تحليل ذكي للبيانات',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.secondary,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          _aiInsights,
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(height: 1.6),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Quick Actions
                Text(
                  'إجراءات سريعة',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        title: 'مشترك جديد',
                        icon: Icons.person_add,
                        color: Theme.of(context).colorScheme.primary,
                        onTap: () {
                          setState(() => _selectedIndex = 1);
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildQuickActionButton(
                        title: 'باقة جديدة',
                        icon: Icons.add_box,
                        color: Theme.of(context).colorScheme.secondary,
                        onTap: () {
                          setState(() => _selectedIndex = 2);
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              fontSize: 11, // Slightly smaller font
            ),
            maxLines: 1, // Limit to one line
            overflow: TextOverflow.ellipsis, // Add ellipsis
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 10, // Slightly smaller font
              ),
              maxLines: 1, // Limit to one line
              overflow: TextOverflow.ellipsis, // Add ellipsis
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
