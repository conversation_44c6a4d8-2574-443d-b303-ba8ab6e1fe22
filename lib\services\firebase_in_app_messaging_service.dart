import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirebaseInAppMessagingService {
  static final FirebaseInAppMessagingService _instance = FirebaseInAppMessagingService._internal();
  factory FirebaseInAppMessagingService() => _instance;
  FirebaseInAppMessagingService._internal();

  final FirebaseInAppMessaging _firebaseInAppMessaging = FirebaseInAppMessaging.instance;
  static const String _isEnabledKey = 'in_app_messaging_enabled';
  static const String _lastMessageShownKey = 'last_in_app_message_shown';

  /// Initialize the Firebase In-App Messaging service
  Future<void> initialize() async {
    try {
      print('==== [InAppMessaging] بدء تهيئة Firebase In-App Messaging ====');
      
      // Check if in-app messaging is enabled
      final isEnabled = await _isInAppMessagingEnabled();
      if (isEnabled) {
        await _setupInAppMessaging();
        print('==== [InAppMessaging] تم تهيئة Firebase In-App Messaging بنجاح ====');
      } else {
        print('==== [InAppMessaging] In-App Messaging معطل ====');
      }
    } catch (e) {
      print('==== [InAppMessaging] خطأ في تهيئة Firebase In-App Messaging: $e ====');
    }
  }

  /// Setup in-app messaging listeners and configuration
  Future<void> _setupInAppMessaging() async {
    try {
      // Firebase In-App Messaging is automatically enabled by default
      // No need to call setMessagesDisplaySuppressed
      print('==== [InAppMessaging] تم إعداد In-App Messaging ====');
    } catch (e) {
      print('==== [InAppMessaging] خطأ في إعداد In-App Messaging: $e ====');
    }
  }

  /// Enable or disable in-app messaging
  Future<void> setInAppMessagingEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isEnabledKey, enabled);
      
      if (enabled) {
        // Firebase In-App Messaging is enabled by default
        print('==== [InAppMessaging] تم تفعيل In-App Messaging ====');
      } else {
        // Note: Firebase In-App Messaging doesn't have a direct disable method
        // The messages will be controlled by Firebase Console
        print('==== [InAppMessaging] تم تعطيل In-App Messaging (يجب التحكم من Firebase Console) ====');
      }
    } catch (e) {
      print('==== [InAppMessaging] خطأ في تغيير حالة In-App Messaging: $e ====');
    }
  }

  /// Check if in-app messaging is enabled
  Future<bool> _isInAppMessagingEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isEnabledKey) ?? true; // Default to enabled
    } catch (e) {
      print('==== [InAppMessaging] خطأ في التحقق من حالة In-App Messaging: $e ====');
      return true; // Default to enabled on error
    }
  }

  /// Get in-app messaging enabled status
  Future<bool> isInAppMessagingEnabled() async {
    return await _isInAppMessagingEnabled();
  }

  /// Trigger a custom event for in-app messaging
  Future<void> triggerEvent(String eventName) async {
    try {
      await _firebaseInAppMessaging.triggerEvent(eventName);
      print('==== [InAppMessaging] تم تشغيل الحدث: $eventName ====');
    } catch (e) {
      print('==== [InAppMessaging] خطأ في تشغيل الحدث: $e ====');
    }
  }

  /// Trigger events for common app actions
  Future<void> triggerAppOpen() async {
    await triggerEvent('app_open');
  }

  Future<void> triggerUserLogin() async {
    await triggerEvent('user_login');
  }

  Future<void> triggerDashboardView() async {
    await triggerEvent('dashboard_view');
  }

  /// Get the last time a message was shown
  Future<DateTime?> getLastMessageShownTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastMessageShownKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      print('==== [InAppMessaging] خطأ في الحصول على وقت آخر رسالة: $e ====');
      return null;
    }
  }

  /// Check if enough time has passed since last message (to avoid spam)
  Future<bool> canShowMessage({Duration minInterval = const Duration(hours: 1)}) async {
    try {
      final lastShown = await getLastMessageShownTime();
      if (lastShown == null) {
        return true; // No message shown before
      }
      
      final timeSinceLastMessage = DateTime.now().difference(lastShown);
      return timeSinceLastMessage >= minInterval;
    } catch (e) {
      print('==== [InAppMessaging] خطأ في التحقق من إمكانية عرض الرسالة: $e ====');
      return true; // Allow on error
    }
  }

  /// Manually trigger a specific in-app message (for testing)
  Future<void> triggerMessage(String messageId) async {
    try {
      // Note: This is a simplified approach. In a real implementation,
      // you would need to use Firebase Remote Config or similar to trigger specific messages
      await _firebaseInAppMessaging.triggerEvent('custom_message_$messageId');
      print('==== [InAppMessaging] تم تشغيل رسالة مخصصة: $messageId ====');
    } catch (e) {
      print('==== [InAppMessaging] خطأ في تشغيل الرسالة المخصصة: $e ====');
    }
  }

  /// Record message impression for analytics
  void _recordMessageImpression() {
    try {
      final prefs = SharedPreferences.getInstance();
      prefs.then((prefs) {
        final now = DateTime.now().millisecondsSinceEpoch;
        prefs.setInt(_lastMessageShownKey, now);
      });
      
      print('==== [InAppMessaging] تم تسجيل عرض الرسالة ====');
    } catch (e) {
      print('==== [InAppMessaging] خطأ في تسجيل عرض الرسالة: $e ====');
    }
  }

  /// Clean up resources
  void dispose() {
    // Firebase In-App Messaging doesn't require explicit disposal
    print('==== [InAppMessaging] تم تنظيف موارد In-App Messaging ====');
  }
} 