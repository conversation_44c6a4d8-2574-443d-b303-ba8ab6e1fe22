import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:isp_manager/services/firebase_auth_service.dart';
import 'package:isp_manager/services/firebase_service.dart';
import 'dart:convert';
import '../services/database_service.dart';
import '../services/ai_service.dart';
import '../services/message_service.dart';
import '../services/printer_service.dart';
import '../services/app_settings_service.dart';
import '../services/whatsapp_service.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../models/user_model.dart';
import '../models/printer_settings_model.dart';
import '../models/activity_log_model.dart';
import '../models/payment_record_model.dart';
import '../widgets/currency_country_widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/sqlite_service.dart'; // Import SQLiteService
import '../services/sas_api_service.dart'; // Import SasApiService
import '../models/sas_user_model.dart'; // Import SasUser
import '../models/sas_server_model.dart'; // Import SasServerModel
import '../models/network_device.dart'; // Import NetworkDevice
import '../services/network_device_service.dart'; // Import NetworkDeviceService
import '../helpers/database_helper.dart'; // Import DatabaseHelper

class SubscriberDetailPage extends StatefulWidget {
  final String subscriberId;

  const SubscriberDetailPage({super.key, required this.subscriberId});

  @override
  State<SubscriberDetailPage> createState() => _SubscriberDetailPageState();
}

class _SubscriberDetailPageState extends State<SubscriberDetailPage>
    with TickerProviderStateMixin {
  SubscriberModel? _subscriber;
  PackageModel? _package;
  List<ActivityLogModel> _activityLogs = [];
  List<PaymentRecordModel> _paymentRecords = [];
  UserModel? _currentUser;
  SasUser? _sasUser; // New state variable for SAS Radius user
  bool _isLoading = true;
  late TabController _tabController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  Map<String, dynamic>? _onlineInfo;
  bool _loadingOnline = false;
  List<PackageModel> _packages = [];

  // متغيرات جديدة لإدارة الأجهزة والإشارة
  NetworkDevice? _networkDevice;
  Map<String, String>? _ubntSignalInfo;
  bool _loadingSignal = false;
  String _tempUsername = '';
  String _tempPassword = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _loadData();
    //  WidgetsBinding.instance
    //     .addPostFrameCallback((_) => serverUserData());
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: isError ? Colors.red : Colors.green,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      var subscribers = await DatabaseService().getSubscribersFire();
      if (subscribers.isEmpty) {
        subscribers = await DatabaseService().getSubscribers();
      }
      var packages = await DatabaseService().getPackagesFire();
      if (packages.isEmpty) {
        packages = await DatabaseService().getPackages();
      }
      var activityLogs = await DatabaseService().getActivityLogsFire();
      if (activityLogs.isEmpty) {
        activityLogs = await DatabaseService().getActivityLogs();
      }
      var paymentRecords = await DatabaseService().getPaymentRecordsFire();
      if (paymentRecords.isEmpty) {
        paymentRecords = await DatabaseService().getPaymentRecords();
      }
      final currentUser = await FirebaseAuthService().getUserData(
        FirebaseAuth.instance.currentUser!.uid,
      );
      DatabaseService().syncPaymentsToFirebase();
      DatabaseService().syncActivityToFirebase();
      final subscriber =
          subscribers.where((s) => s.id == widget.subscriberId).isNotEmpty
          ? subscribers.where((s) => s.id == widget.subscriberId).first
          : null;

      if (subscriber != null) {
        final package =
            packages.where((p) => p.id == subscriber.packageId).isNotEmpty
            ? packages.where((p) => p.id == subscriber.packageId).first
            : null;

        final subscriberLogs =
            activityLogs
                .where((log) => log.subscriberId == subscriber.id)
                .toList()
              ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

        final subscriberPayments =
            paymentRecords
                .where((record) => record.subscriberId == subscriber.id)
                .toList()
              ..sort(
                (a, b) => b.paymentDate.compareTo(a.paymentDate),
              );
        try {
          setState(() {
            print('Setting state with subscriber: ${subscriber.fullName}');
            _subscriber = subscriber;
            _package = package;
            _activityLogs = subscriberLogs;
            _paymentRecords = subscriberPayments;
            _currentUser = currentUser;
            _packages = packages;
            _isLoading = false;
            print('State set successfully');
          });
          serverUserData();
          // جلب معلومات الجلسة تلقائياً بعد تحميل البيانات
          _fetchOnlineInfo();
        } catch (e) {
          print('Error in setState: $e');
          if (mounted) {
            setState(() => _isLoading = false);
          }
        }

        try {
          _fadeController.forward();
        } catch (e) {
          print('Error in fadeController.forward: $e');
        }
      } else {
        print('Subscriber not found, navigating back');
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      print('Error in _loadData: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> serverUserData() async {
    SasUser? sasUser;
    if (_subscriber!.username.isNotEmpty) {
      SasServerModel? sasServer;
      if (_subscriber!.sasServerId != null) {
        final serverMap = await FirebaseService().getSasServerById(
          _subscriber!.sasServerId!,
        );
        if (serverMap != null) {
          sasServer = SasServerModel.fromMap(serverMap);
        }
      }

      if (sasServer == null) {
        // If no specific server linked, try to use the currently connected one
        final connectedServerMap = await FirebaseService()
            .getConnectedSasServer();
        if (connectedServerMap != null) {
          sasServer = SasServerModel.fromMap(connectedServerMap);
        }
      }

      if (sasServer == null) {
        // Try to use any available active server
        final allServers = await FirebaseService().getSasServers();
        if (allServers.isNotEmpty) {
          sasServer = SasServerModel.fromMap(allServers.first);
        }
      }

      if (sasServer != null) {
        final sasApiService = SasApiService(); // No parameters needed now
        final loggedIn = await sasApiService.login();
        if (loggedIn) {
          sasUser = await sasApiService.getSasUSerDetails(
            _subscriber!.username,
          );
        } else {
          print('Failed to log in to SAS API with server: ${sasServer.name}');
        }
      } else {
        print('No SAS server configured or connected for this subscriber.');
      }
    }
    setState(() {
      _sasUser = sasUser;
      print('State set successfully');
    });
  }

  Future<void> _renewSubscription() async {
    if (_subscriber == null) return;

    // احصل على جميع الباقات المتوفرة
    final packages = await DatabaseService().getPackagesFire();
    print(packages);
    // افتح Dialog التجديد مباشرة، ودع المستخدم يختار الباقة من داخله
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _RenewalDialog(
        subscriber: _subscriber!,
        package: _package, // قد تكون null إذا لم يكن للمشترك باقة
        allPackages: packages,
      ),
    );

    if (result == true) {
      await _loadData();
      // عرض رسالة نجاح التجديد
      if (mounted) {
        _showSafeSnackBar('تم تجديد اشتراك ${_subscriber?.fullName} بنجاح.');
      }
    }
  }

  Future<void> _recordPayment() async {
    if (_subscriber == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) =>
          _PaymentDialog(subscriber: _subscriber!, currentUser: _currentUser!),
    );

    if (result == true) {
      await _loadData();
    }
  }

  Future<void> _generatePaymentReminder() async {
    if (_subscriber == null || _package == null) return;

    showDialog(
      context: context,
      builder: (context) =>
          _PaymentReminderDialog(subscriber: _subscriber!, package: _package!),
    );
  }

  Future<void> _editSubscriber() async {
    if (_subscriber == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _EditSubscriberDialog(subscriber: _subscriber!),
    );

    if (result == true) {
      await _loadData();
    }
  }

  Future<void> _addPreviousDebt() async {
    if (_subscriber == null || _currentUser == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _PreviousDebtDialog(
        subscriber: _subscriber!,
        currentUser: _currentUser!,
      ),
    );

    if (result == true) {
      await _loadData();
    }
  }

  Future<void> _showEditDeviceCredentials() async {
    if (_networkDevice == null) return;

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _EditDeviceCredentialsDialog(
        device: _networkDevice!,
        currentUsername: _tempUsername,
        currentPassword: _tempPassword,
      ),
    );

    if (result != null) {
      setState(() {
        _tempUsername = result['username']!;
        _tempPassword = result['password']!;
      });

      // تحديث بيانات الجهاز في Firebase
      await _updateDeviceCredentials(result['username']!, result['password']!);

      // إعادة جلب معلومات الإشارة
      await _fetchSignalInfo();

      if (mounted) {
        _showSafeSnackBar('تم تحديث بيانات الدخول وإعادة جلب معلومات الإشارة');
      }
    }
  }

  Future<void> _updateDeviceCredentials(String username, String password) async {
    if (_networkDevice == null) return;

    try {
      final dbHelper = DatabaseHelper();

      // إنشاء جهاز محدث
      final updatedDevice = NetworkDevice(
        id: _networkDevice!.id,
        name: _networkDevice!.name,
        adminId: _networkDevice!.adminId,
        ipAddress: _networkDevice!.ipAddress,
        type: _networkDevice!.type,
        username: _networkDevice!.username,
        password: _networkDevice!.password,
        ubntUsername: username,
        ubntPassword: password,
      );

      await dbHelper.updateNetworkDevice(updatedDevice);

      setState(() {
        _networkDevice = updatedDevice;
      });
    } catch (e) {
      print('Error updating device credentials: $e');
      if (mounted) {
        _showSafeSnackBar('فشل في تحديث بيانات الدخول: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('تفاصيل المشترك')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_subscriber == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('خطأ')),
        body: const Center(child: Text('لم يتم العثور على المشترك')),
      );
    }

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            _buildSliverAppBar(),
          ],
          body: Column(
            children: [
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildInfoTab(),
                    _buildPaymentsTab(),
                    _buildActivityTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildActionButtons(),
    );
  }

  Widget _buildSliverAppBar() {
    Color statusColor;
    String statusText = _subscriber!.subscriptionStatusText;
    IconData statusIcon;

    if (_subscriber!.isExpired) {
      statusColor = Theme.of(context).colorScheme.error;
      statusIcon = Icons.error_outline;
    } else if (_subscriber!.isExpiringSoon) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning_amber;
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle_outline;
    }
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        // زر إرسال تنبيه انتهاء الاشتراك (يظهر فقط للمشتركين الذين ينتهي اشتراكهم قريباً)
        if (_subscriber!.isExpiringSoon)
          IconButton(
            icon: const Icon(Icons.notification_important),
            tooltip: 'إرسال تنبيه انتهاء الاشتراك',
            onPressed: _sendExpiryNotification,
          ),
        // زر تعديل بيانات الدخول للجهاز (أيقونة ربط صغيرة)
        IconButton(
          icon: Icon(
            _networkDevice != null ? Icons.link : Icons.link_off,
            size: 20,
            color: _networkDevice != null ? null : Colors.grey,
          ),
          tooltip: _getDeviceLinkTooltip(),
          onPressed: _networkDevice != null
            ? _showEditDeviceCredentials
            : _showNoDeviceMessage,
        ),
        // زر حذف المشترك
        IconButton(
          icon: const Icon(Icons.delete_outline),
          tooltip: 'حذف المشترك',
          onPressed: _showDeleteConfirmation,
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                statusColor.withOpacity(0.1),
                Theme.of(context).colorScheme.surface,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(statusIcon, color: statusColor, size: 24),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _subscriber!.fullName,
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _subscriber!.phoneNumber,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurface.withOpacity(0.7),
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          statusText,
                          style: Theme.of(context).textTheme.labelMedium
                              ?.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(
          context,
        ).colorScheme.onSurface.withOpacity(0.6),
        indicatorColor: Theme.of(context).colorScheme.primary,
        tabs: const [
          Tab(text: 'المعلومات'),
          Tab(text: 'المدفوعات'),
          Tab(text: 'السجل'),
        ],
      ),
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection(
            title: 'معلومات الاشتراك',
            icon: Icons.inventory,
            children: [
              _buildInfoRow(
                'الباقة',
                _subscriber!.packageName,
              ), // Use the new packageName field
              _buildInfoRowWithWidget(
                'السعر',
                _package != null
                    ? CurrencyText(
                        amount: _package!.sellingPrice ?? _package!.price,
                      )
                    : const Text('غير محدد'),
              ),
              _buildInfoRow('المدة', _package?.durationDisplayText ?? 'غير متوفر'),
              _buildInfoRow('السرعة', _package?.speed ?? 'غير متوفر'),
              _buildInfoRow('عدد الأجهزة', _package != null ? '${_package!.deviceCount}' : 'غير متوفر'),
              _buildInfoRow(
                'تاريخ البداية',
                _formatDateTime(_subscriber!.subscriptionStart),
              ),
              _buildInfoRow(
                'تاريخ الانتهاء',
                _formatDateTime(_subscriber!.subscriptionEnd),
              ),
              _buildInfoRow(
                'الأيام المتبقية',
                _subscriber!.daysRemaining != null
                    ? '${_subscriber!.daysRemaining}'
                    : 'غير محدد',
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoSection(
            title: 'معلومات الاتصال',
            icon: Icons.contact_phone,
            children: [
              _buildInfoRow('الاسم الكامل', _subscriber!.fullName),
              _buildInfoRowWithWidget(
                'رقم الهاتف',
                PhoneText(phoneNumber: _subscriber!.phoneNumber),
              ),
              _buildInfoRow('العنوان', _subscriber!.address),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoSection(
            title: 'المعلومات الفنية',
            icon: Icons.router,
            children: [
              _buildInfoRow(
                'نوع الاشتراك',
                _subscriber!.subscriptionTypeDisplayText,
              ),
              _buildInfoRow(
                'اسم المستخدم',
                _subscriber!.username.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.username,
              ),
              _buildInfoRow(
                'كلمة المرور',
                _subscriber!.password.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.password,
              ),
              _buildInfoRow(
                'عنوان MAC',
                _subscriber!.macAddress.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.macAddress,
              ),
              _buildInfoRow(
                'اسم الراوتر',
                _subscriber!.routerName.isEmpty
                    ? 'غير محدد'
                    : _subscriber!.routerName,
              ),
              _buildInfoRow(
                'الملاحظات الفنية',
                _subscriber!.technicalNotes.isEmpty
                    ? 'لا توجد ملاحظات'
                    : _subscriber!.technicalNotes,
              ),
            ],
          ),
          const SizedBox(height: 20),
          // عرض قسم الدين دائماً مع الألوان المناسبة
          _buildDebtSection(),
          const SizedBox(height: 20),
          // New section for SAS Radius Information
          if (_sasUser != null)
            _buildInfoSection(
              title: 'معلومات SAS Radius',
              icon: Icons.cloud,
              children: [
                _buildInfoRow('اسم المستخدم (SAS)', _sasUser!.username),
                _buildInfoRow("اسم الباقة", _package?.name ?? 'غير متوفر'),
                _buildInfoRowWithWidget(
                  'الرصيد (SAS)',
                  _sasUser!.balance != null
                      ? CurrencyText(amount: _sasUser!.balance!)
                      : const Text('غير متوفر'),
                ),
                _buildInfoRow('الحالة (SAS)', _sasUser!.status ?? 'غير متوفر'),
                _buildInfoRow('الاسم الكامل (SAS)', _sasUser!.fullName),
                _buildInfoRow(
                  'تاريخ الانتهاء',
                  _sasUser!.expiration.toString(),
                ),
                _buildInfoRow(
                  'البريد الالكترونى',
                  (_sasUser!.email != null)
                      ? _sasUser!.email.toString()
                      : 'غير متوفر',
                ),
                _buildInfoRow(
                  "العنوان",
                  (_sasUser!.address!.isNotEmpty)
                      ? _sasUser!.address.toString()
                      : 'غير متوفر',
                ),
                _buildInfoRow(
                  "المدينة",
                  (_sasUser!.city!.isNotEmpty)
                      ? _sasUser!.city.toString()
                      : 'غير متوفر',
                ),

                _buildInfoRowWithWidget(
                  'رقم الهاتف (SAS)',
                  _sasUser!.phone != null
                      ? PhoneText(phoneNumber: _sasUser!.phone!)
                      : const Text('غير متوفر'),
                ),
              ],
            ),
          const SizedBox(height: 16),
          const Text('معلومات الجلسة الحالية:', style: TextStyle(fontWeight: FontWeight.bold)),
          Row(
            children: [
              ElevatedButton(
                onPressed: _fetchOnlineInfo,
                child: const Text('تحديث حالة الاتصال'),
              ),
              const SizedBox(width: 8),
              if (_networkDevice != null)
                ElevatedButton.icon(
                  onPressed: _loadingSignal ? null : _fetchSignalInfo,
                  icon: _loadingSignal
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2)
                      )
                    : const Icon(Icons.refresh),
                  label: const Text('تحديث الإشارة'),
                ),
            ],
          ),
          buildOnlineInfo(),
          const SizedBox(height: 16),
          _buildSignalInfoSection(),
        ],
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return Column(
      children: [
        // عرض حالة الدين (دائماً مرئي مع الألوان الجديدة)
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _subscriber!.debtStatusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _subscriber!.debtStatusColor.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _subscriber!.debtStatusIcon,
                color: _subscriber!.debtStatusColor,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _subscriber!.hasOutstandingDebt
                          ? 'إجمالي الدين المستحق'
                          : _subscriber!.hasAdvancePayment
                          ? 'رصيد مدفوع مقدماً'
                          : 'حالة الدين',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _subscriber!.debtStatusColor,
                      ),
                    ),
                    FutureBuilder<String>(
                      future: _subscriber!.hasOutstandingDebt
                          ? AppSettingsService.formatCurrency(
                              _subscriber!.debtAmount,
                            )
                          : _subscriber!.hasAdvancePayment
                          ? AppSettingsService.formatCurrency(
                              _subscriber!.advancePaymentAmount,
                            )
                          : Future.value('لا توجد ديون مستحقة'),
                      builder: (context, snapshot) {
                        return Text(
                          snapshot.data ?? '...',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: _subscriber!.debtStatusColor,
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              // زر الدفع (متاح دائماً)
              ElevatedButton.icon(
                onPressed: _recordPayment,
                icon: const Icon(Icons.payment),
                label: Text(
                  _subscriber!.hasOutstandingDebt ? 'دفع' : 'إضافة دفعة',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _subscriber!.debtStatusColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _paymentRecords.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.payment,
                        size: 64,
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مدفوعات مسجلة',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withOpacity(0.6),
                            ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _paymentRecords.length,
                  itemBuilder: (context, index) {
                    final record = _paymentRecords[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.check_circle, color: Colors.green),
                        ),
                        title: FutureBuilder<String>(
                          future: AppSettingsService.formatCurrency(
                            record.amount,
                          ),
                          builder: (context, snapshot) {
                            return Text(
                              snapshot.data ?? '...',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('طريقة الدفع: ${record.paymentMethod}'),
                            Text(_formatDate(record.paymentDate)),
                            if (record.notes != null &&
                                record.notes!.isNotEmpty)
                              Text('ملاحظات: ${record.notes}'),
                          ],
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildActivityTab() {
    return _activityLogs.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.history,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد أنشطة مسجلة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _activityLogs.length,
            itemBuilder: (context, index) {
              final log = _activityLogs[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getActivityIcon(log.action),
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  title: Text(
                    log.action,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(log.description),
                      Text(_formatDateTime(log.timestamp)),
                      if (log.amount > 0)
                        FutureBuilder<String>(
                          future: AppSettingsService.formatCurrency(log.amount),
                          builder: (context, snapshot) {
                            return Text(
                              'المبلغ: ${snapshot.data ?? '...'}',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isCopyable = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: isCopyable && value != '-'
                ? GestureDetector(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: value));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('تم نسخ $label: $value')),
                      );
                    },
                    child: Text(
                      value,
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  )
                : Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWithWidget(String label, Widget valueWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  Widget _buildDebtSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _subscriber!.debtStatusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _subscriber!.debtStatusColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _subscriber!.debtStatusIcon,
                color: _subscriber!.debtStatusColor,
              ),
              const SizedBox(width: 8),
              Text(
                _subscriber!.hasOutstandingDebt
                    ? 'دين مستحق'
                    : _subscriber!.hasAdvancePayment
                    ? 'رصيد مقدم'
                    : 'حالة الدين',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _subscriber!.debtStatusColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _subscriber!.debtStatusText,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _subscriber!.debtStatusColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              if (_subscriber!.hasOutstandingDebt) ...[
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _generatePaymentReminder,
                    icon: const Icon(Icons.message),
                    label: const Text('رسالة تذكير'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _subscriber!.debtStatusColor,
                      side: BorderSide(color: _subscriber!.debtStatusColor),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _recordPayment,
                  icon: const Icon(Icons.payment),
                  label: Text(
                    _subscriber!.hasOutstandingDebt
                        ? 'تسجيل دفعة'
                        : _subscriber!.hasAdvancePayment
                        ? 'إضافة رصيد'
                        : 'تسجيل دفعة',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _subscriber!.debtStatusColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر إضافة دين سابق
        FloatingActionButton(
          onPressed: _addPreviousDebt,
          backgroundColor: Theme.of(context).colorScheme.error,
          foregroundColor: Theme.of(context).colorScheme.onError,
          heroTag: 'add-debt',
          tooltip: 'إضافة دين سابق',
          child: const Icon(Icons.add_card),
        ),
        const SizedBox(height: 8),
        // زر تجديد الاشتراك
        FloatingActionButton(
          onPressed: _renewSubscription,
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          heroTag: 'renew',
          tooltip: 'تجديد الاشتراك',
          child: const Icon(Icons.refresh),
        ),
        const SizedBox(height: 8),
        // زر تعديل المشترك
        FloatingActionButton(
          onPressed: _editSubscriber,
          backgroundColor: Theme.of(context).colorScheme.secondary,
          foregroundColor: Theme.of(context).colorScheme.onSecondary,
          heroTag: 'edit',
          tooltip: 'تعديل المشترك',
          child: const Icon(Icons.edit),
        ),
      ],
    );
  }

  IconData _getActivityIcon(String action) {
    switch (action) {
      case 'إضافة مشترك':
        return Icons.person_add;
      case 'تجديد اشتراك':
        return Icons.refresh;
      case 'تسجيل دفعة':
        return Icons.payment;
      case 'تعديل بيانات':
        return Icons.edit;
      case 'إضافة دين سابق':
        return Icons.add_card;
      default:
        return Icons.history;
    }
  }

  static String _formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'غير محدد';
    return DateFormat('yyyy/MM/dd hh:mm a').format(dateTime);
  }

  // دالة إرسال تنبيه انتهاء الاشتراك
  void _sendExpiryNotification() async {
    try {
      final messageService = MessageService();

      // إرسال رسالة انتهاء الاشتراك باستخدام القالب
      await messageService.sendExpiryReminder(_subscriber!);
      if (mounted) {
        _showSafeSnackBar('تم إرسال تنبيه انتهاء الاشتراك بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showSafeSnackBar('فشل في إرسال التنبيه: $e', isError: true);
      }
    }
  }

  // دالة إظهار تأكيد حذف المشترك
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('هل أنت متأكد من حذف المشترك "${_subscriber!.fullName}"؟'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تحذير: لا يمكن التراجع عن هذا الإجراء. سيتم حذف جميع البيانات المرتبطة بالمشترك.',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteSubscriber();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  // دالة حذف المشترك
  void _deleteSubscriber() async {
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (BuildContext context) {
    //     return const AlertDialog(
    //       content: Row(
    //         children: [
    //           CircularProgressIndicator(),
    //           SizedBox(width: 16),
    //           Text('جاري حذف المشترك...'),
    //         ],
    //       ),
    //     );
    //   },
    // );

    try {
      await DatabaseService().deleteSubscriber(_subscriber!.id);

      if (mounted) {
        Navigator.of(context).pop(true); // العودة مع إشارة أن المشترك تم حذفه

        _showSafeSnackBar('تم حذف المشترك بنجاح');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق dialog التحميل

        _showSafeSnackBar('فشل في حذف المشترك: $e', isError: true);
      }
    }
  }

  Future<void> _fetchOnlineInfo() async {
    if (_subscriber == null || _subscriber!.username.isEmpty) return;
    setState(() => _loadingOnline = true);
    try {
      // جلب السيرفر المتصل حاليًا
      SasServerModel? sasServer;
      if (_subscriber!.sasServerId != null) {
        final serverMap = await FirebaseService().getSasServerById(
          _subscriber!.sasServerId!,
        );
        if (serverMap != null) {
          sasServer = SasServerModel.fromMap(serverMap);
        }
      }
      if (sasServer == null) {
        final connectedServerMap = await FirebaseService().getConnectedSasServer();
        if (connectedServerMap != null) {
          sasServer = SasServerModel.fromMap(connectedServerMap);
        }
      }
      if (sasServer == null) {
        final allServers = await FirebaseService().getSasServers();
        if (allServers.isNotEmpty) {
          sasServer = SasServerModel.fromMap(allServers.first);
        }
      }
      if (sasServer != null) {
        final sasApiService = SasApiService();
        final loggedIn = await sasApiService.loginWithCredentials(
          host: sasServer.host,
          username: sasServer.username,
          password: sasServer.password,
        );
        if (loggedIn) {
          final token = sasApiService.token;
          final onlineInfo = await sasApiService.fetchOnlineUserInfo(
            token: token!,
            username: _subscriber!.username,
            host: sasServer.host,
            useHttps: sasServer.host.startsWith('https'),
          );
          setState(() {
            _onlineInfo = onlineInfo;
          });

          // بعد جلب معلومات الجلسة، جلب الجهاز الشبكي
          await _fetchNetworkDevice();
        } else {
          setState(() {
            _onlineInfo = null;
          });
        }
      } else {
        setState(() {
          _onlineInfo = null;
        });
      }
    } catch (e) {
      setState(() {
        _onlineInfo = null;
      });
    } finally {
      setState(() => _loadingOnline = false);
    }
  }

  Future<void> _fetchNetworkDevice() async {
    if (_onlineInfo == null ||
        _onlineInfo!['data'] == null ||
        _onlineInfo!['data'].isEmpty) return;

    try {
      final session = _onlineInfo!['data'][0];
      final ipAddress = session['framedipaddress']?.toString();

      if (ipAddress != null && ipAddress.isNotEmpty) {
        final dbHelper = DatabaseHelper();
        final device = await dbHelper.getNetworkDeviceByIpFire(ipAddress);

        if (device != null) {
          setState(() {
            _networkDevice = device;
            // تعيين بيانات الدخول المؤقتة
            _tempUsername = device.ubntUsername ?? device.username;
            _tempPassword = device.ubntPassword ?? device.password;
          });

          // جلب معلومات الإشارة تلقائياً
          await _fetchSignalInfo();
        }
      }
    } catch (e) {
      print('Error fetching network device: $e');
    }
  }

  Future<void> _fetchSignalInfo() async {
    if (_networkDevice == null) return;

    setState(() => _loadingSignal = true);
    try {
      final deviceService = NetworkDeviceService();

      // إنشاء جهاز مؤقت ببيانات الدخول المحدثة
      final tempDevice = NetworkDevice(
        id: _networkDevice!.id,
        name: _networkDevice!.name,
        adminId: _networkDevice!.adminId,
        ipAddress: _networkDevice!.ipAddress,
        type: _networkDevice!.type,
        username: _tempUsername,
        password: _tempPassword,
      );

      final signalInfo = await deviceService.getDeviceInfo(tempDevice);

      setState(() {
        _ubntSignalInfo = signalInfo;
      });
    } catch (e) {
      print('Error fetching signal info: $e');
      setState(() {
        _ubntSignalInfo = null;
      });
    } finally {
      setState(() => _loadingSignal = false);
    }
  }

  Widget buildOnlineInfo() {
    if (_loadingOnline) {
      return Center(child: CircularProgressIndicator());
    }
    if (_onlineInfo != null && _onlineInfo!['data'] != null && _onlineInfo!['data'].isNotEmpty) {
      final session = _onlineInfo!['data'][0];
      String? packageName;
      final profileId = session['profile_id']?.toString() ?? session['sasProfileId']?.toString();
      if (profileId != null && profileId.isNotEmpty && _packages.isNotEmpty) {
        final found = _packages.firstWhere(
          (p) => p.sasProfileId == profileId || p.id == profileId,
          orElse: () => PackageModel(
            id: 'unknown',
            name: 'غير متوفر',
            price: 0,
            durationInDays: 0,
            speed: '',
            deviceCount: 0,
            adminId: '',
            serverId: '',
            createdAt: DateTime.now(),
          ),
        );
        packageName = found.name;
      }
      // تنسيق مدة الجلسة
      String sessionDuration = '-';
      if (session['acctsessiontime'] != null) {
        final seconds = int.tryParse(session['acctsessiontime'].toString()) ?? 0;
        sessionDuration = formatDuration(seconds);
      }
      return Card(
        margin: EdgeInsets.all(8),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoRow(
                'IP',
                session['framedipaddress'] ?? '-',
                isCopyable: true,
              ),
              _buildInfoRow('مدة الجلسة', sessionDuration),
              _buildInfoRow('وقت البدء', session['acctstarttime']?.toString() ?? '-'),
              _buildInfoRow('آخر ظهور', session['last_online']?.toString() ?? '-'),
              _buildInfoRow('اسم الباقة (الجلسة)', packageName ?? 'غير متوفر'),
              _buildInfoRow('البيانات المستلمة (RX)', formatBytes(session['acctinputoctets'])),
              _buildInfoRow('البيانات المرسلة (TX)', formatBytes(session['acctoutputoctets'])),
            ],
          ),
        ),
      );
    } else {
      return Text('لا يوجد جلسة حالية للمشترك');
    }
  }

  Widget _buildSignalInfoSection() {
    if (_networkDevice == null) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.device_unknown,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'لم يتم العثور على جهاز مرتبط بهذا المشترك',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تأكد من وجود جهاز بنفس IP الجلسة في قائمة الأجهزة',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (_loadingSignal) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'جاري جلب معلومات الإشارة...',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_ubntSignalInfo == null) {
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.signal_wifi_off,
                size: 48,
                color: Colors.red.shade400,
              ),
              const SizedBox(height: 8),
              Text(
                'فشل في جلب معلومات الإشارة',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تحقق من بيانات الدخول للجهاز',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _showEditDeviceCredentials,
                icon: const Icon(Icons.settings),
                label: const Text('تعديل بيانات الدخول'),
              ),
            ],
          ),
        ),
      );
    }

    return buildUbntSignalInfo(_ubntSignalInfo!);
  }

  Widget buildUbntSignalInfo(Map<String, String> ubntInfo) {
    Color getSignalColor(String? value) {
      if (value == null) return Colors.grey;
      final match = RegExp(r'-?\d+').firstMatch(value);
      if (match == null) return Colors.grey;
      final dbm = int.tryParse(match.group(0)!);
      if (dbm == null) return Colors.grey;
      if (dbm >= -60) return Colors.green;
      if (dbm >= -75) return Colors.orange;
      return Colors.red;
    }
    Widget copyableRow(String label, String? value, {IconData? icon}) {
      return GestureDetector(
        onTap: value != null && value != '-' ? () {
          Clipboard.setData(ClipboardData(text: value));
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم نسخ $label: $value')),
          );
        } : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            children: [
              if (icon != null) Icon(icon, size: 20, color: Colors.blueGrey),
              if (icon != null) SizedBox(width: 6),
              Expanded(flex: 2, child: Text(label, style: TextStyle(fontWeight: FontWeight.bold))),
              Expanded(flex: 3, child: Text(value ?? '-', style: TextStyle(color: Colors.black87))),
              if (value != null && value != '-') Icon(Icons.copy, size: 16, color: Colors.grey.shade400),
            ],
          ),
        ),
      );
    }
    return Card(
      margin: EdgeInsets.all(8),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.network_check, color: getSignalColor(ubntInfo['signalStrength']), size: 28),
                SizedBox(width: 8),
                Text(
                  ubntInfo['signalStrength'] ?? '-',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: getSignalColor(ubntInfo['signalStrength']),
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  getSignalColor(ubntInfo['signalStrength']) == Colors.green
                    ? '(جيدة)'
                    : getSignalColor(ubntInfo['signalStrength']) == Colors.orange
                      ? '(متوسطة)'
                      : '(ضعيفة)',
                  style: TextStyle(fontSize: 14, color: getSignalColor(ubntInfo['signalStrength'])),
                ),
              ],
            ),
            SizedBox(height: 8),
            copyableRow('SNR', ubntInfo['snr'], icon: Icons.ssid_chart),
            copyableRow('Noise', ubntInfo['noise'], icon: Icons.noise_aware),
            copyableRow('SSID', ubntInfo['ssid'], icon: Icons.wifi),
            copyableRow('سرعة الإرسال', ubntInfo['txRate'], icon: Icons.upload),
            copyableRow('سرعة الاستقبال', ubntInfo['rxRate'], icon: Icons.download),
            copyableRow('مدة التشغيل', ubntInfo['uptime'], icon: Icons.timer),
            copyableRow('اسم الجهاز', ubntInfo['deviceName'], icon: Icons.device_hub),
            copyableRow('الموديل', ubntInfo['deviceModel'], icon: Icons.memory),
            copyableRow('MAC', ubntInfo['macAddress'], icon: Icons.qr_code),
            copyableRow('إصدار السوفتوير', ubntInfo['firmwareVersion'], icon: Icons.system_update),
            copyableRow('وضع الشبكة', ubntInfo['networkMode'], icon: Icons.settings_ethernet),
            copyableRow('وضع الواي فاي', ubntInfo['wirelessMode'], icon: Icons.wifi_tethering),
            copyableRow('سرعة LAN', ubntInfo['lanSpeed'], icon: Icons.cable),
          ],
        ),
      ),
    );
  }
}

class _RenewalDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final PackageModel? package;
  final List<PackageModel> allPackages;

  const _RenewalDialog({
    required this.subscriber,
    required this.package,
    required this.allPackages,
  });

  @override
  State<_RenewalDialog> createState() => _RenewalDialogState();
}

class _RenewalDialogState extends State<_RenewalDialog> {
  bool _isLoading = false;
  bool? _isPaid; // Changed to nullable bool
  bool? _sendMessage; // Changed to nullable bool
  PrinterSettingsModel? _printerSettings;
  bool _printerEnabled = false;
  bool _printerLoading = true;
  late String _selectedPackageId;
  late PackageModel _selectedPackage;
  final _notesController = TextEditingController();
  final _amountController = TextEditingController();
  late String _paymentMethod; // Changed to late
  final List<String> _paymentMethods = [
    'نقداً',
    'كاش آسيا',
    'زين كاش',
    'آسيا حوالة',
    'حوالة بنكية',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    // Initialize nullable booleans
    _isPaid = false;
    _sendMessage = false;
    _paymentMethod = _paymentMethods.first; // Initialize payment method

    // Filter packages to avoid duplicates and empty IDs
    final validPackages = widget.allPackages
        .where((package) => package.id.isNotEmpty)
        .toSet()
        .toList();

    // إذا كانت الباقة غير محددة أو غير صالحة، اختر أول باقة صالحة من القائمة
    if (widget.package != null &&
        validPackages.any((p) => p.id == widget.package!.id)) {
      _selectedPackage = widget.package!;
    } else {
      _selectedPackage = validPackages.isNotEmpty
          ? validPackages.first
          : widget.allPackages.first;
    }
    _selectedPackageId = _selectedPackage.id;
    _amountController.text =
        (_selectedPackage.sellingPrice ?? _selectedPackage.price).toString();
    _loadPrinterSettings();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: isError ? Colors.red : Colors.green,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final json = prefs.getString('printer_settings');
    if (json != null) {
      final settings = PrinterSettingsModel.fromJson(jsonDecode(json));
      setState(() {
        _printerSettings = settings;
        _printerEnabled =
            settings.connectedDeviceAddress != null &&
            settings.connectedDeviceAddress!.isNotEmpty;
        _printerLoading = false;
      });
    } else {
      setState(() {
        _printerSettings = null;
        _printerEnabled = false;
        _printerLoading = false;
      });
    }
  }

  Future<void> _printReceipt() async {
    if (_printerSettings == null) return;
    try {
      // إظهار رسالة تحميل فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        try {
          _showSafeSnackBar('جاري الطباعة...');
        } catch (scaffoldError) {
          print('جاري الطباعة...');
        }
      }

      final now = DateTime.now();
      final data = {
        'subscriberName': widget.subscriber.fullName,
        'subscriptionNumber': widget.subscriber.id,
        'dateTime':
            '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour}:${now.minute.toString().padLeft(2, '0')}',
        'paymentAmount':
            _selectedPackage.sellingPrice ?? _selectedPackage.price,
        'operationType': 'تجديد اشتراك',
        'employeeName': '',
        'companyInfo': _printerSettings?.companyName ?? '',
      };

      // طباعة الإيصال بالصورة (حل مشاكل التشفير العربي)
      await PrinterService.printReceiptUnified(
        settings: _printerSettings!,
        data: data,
        operationType: 'تجديد',
        asImage: true, // استخدام الطباعة بالصورة دائماً
      );
      // رسالة نجاح فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        try {
          _showSafeSnackBar('تم طباعة إيصال التجديد بنجاح');
        } catch (scaffoldError) {
          print('تم طباعة إيصال التجديد بنجاح');
        }
      }
    } catch (e) {
      // رسالة خطأ فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        try {
          _showSafeSnackBar('خطأ في الطباعة: $e', isError: true);
        } catch (scaffoldError) {
          print('خطأ في الطباعة: $e');
        }
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تجديد الاشتراك'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تجديد اشتراك: ${widget.subscriber.fullName}'),
            const SizedBox(height: 12),
            // Package selection dropdown
            DropdownButtonFormField<String>(
              value:
                  (widget.allPackages
                          .where((package) => package.id == _selectedPackageId)
                          .length ==
                      1)
                  ? _selectedPackageId
                  : null,
              decoration: const InputDecoration(
                labelText: 'الباقة',
                border: OutlineInputBorder(),
              ),
              isExpanded: true,
              items: widget.allPackages
                  .where((package) => package.id.isNotEmpty)
                  .toSet()
                  .map((package) {
                    return DropdownMenuItem(
                      value: package.id,
                      child: PackageDisplayText(
                        package: package,
                        style: const TextStyle(overflow: TextOverflow.ellipsis),
                      ),
                    );
                  })
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPackageId = value;
                    _selectedPackage = widget.allPackages.firstWhere(
                      (p) => p.id == value,
                    );
                  });
                }
              },
            ),
            const SizedBox(height: 12),
            Text('المدة: ${_selectedPackage.durationDisplayText}'),
            Row(
              children: [
                const Text('السعر: '),
                CurrencyText(
                  amount:
                      _selectedPackage.sellingPrice ?? _selectedPackage.price,
                ),
              ],
            ),
            const Divider(height: 24),

            // Payment status switch
            SwitchListTile(
              title: const Text('تم الدفع'),
              subtitle: const Text('حدد إذا تم دفع قيمة التجديد'),
              value: _isPaid ?? false, // Use null-aware operator
              onChanged: (value) {
                setState(() {
                  _isPaid = value;
                });
              },
              activeColor: Theme.of(context).colorScheme.primary,
            ),

            // إضافة خيار إرسال رسالة
            SwitchListTile(
              title: const Text('إرسال رسالة تجديد'),
              subtitle: const Text('إرسال رسالة للمشترك بعد تجديد الاشتراك'),
              value: _sendMessage ?? false, // Use null-aware operator
              onChanged: (value) {
                setState(() {
                  _sendMessage = value;
                });
              },
              activeColor: Theme.of(context).colorScheme.secondary,
              secondary: Icon(
                Icons.message,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),

            // Payment details (only shown if paid)
            if (_isPaid ?? false) ...[
              // Use null-aware operator
              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: _paymentMethod,
                decoration: const InputDecoration(
                  labelText: 'طريقة الدفع',
                  border: OutlineInputBorder(),
                ),
                items: _paymentMethods.map((method) {
                  return DropdownMenuItem(value: method, child: Text(method));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                  });
                },
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        if (!_printerLoading && _printerEnabled)
          OutlinedButton.icon(
            onPressed: _isLoading ? null : _printReceipt,
            icon: const Icon(Icons.print),
            label: const Text('طباعة الشريط'),
          ),
        ElevatedButton(
          onPressed: _isLoading ? null : _processRenewal,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('تجديد'),
        ),
      ],
    );
  }

  Future<void> _processRenewal() async {
    print('🔄 بدء عملية التجديد...');

    print('✅ جميع القيم المطلوبة متوفرة');
    setState(() => _isLoading = true);

    try {
      final now = DateTime.now();
      final newEndDate = now.add(
        Duration(days: _selectedPackage.durationInDays),
      );

      double newDebtAmount = widget.subscriber.debtAmount;
      final renewalAmount =
          _selectedPackage.sellingPrice ?? _selectedPackage.price;
      if (!(_isPaid ?? false)) {
        // Use null-aware operator
        // إذا لم يتم الدفع، أضف قيمة الباقة إلى الديون المستحقة
        newDebtAmount += renewalAmount;
      }
      // Update subscriber with new subscription dates and possibly new package
      var updatedSubscriber = widget.subscriber.copyWith(
        adminId: FirebaseAuth.instance.currentUser!.uid,
        subscriptionStart: now,
        subscriptionEnd: newEndDate,
        packageId: _selectedPackageId,
        debtAmount: newDebtAmount,
        paymentStatus: (_isPaid ?? false)
            ? PaymentStatus.paid
            : PaymentStatus.pending, // Use null-aware operator
      );

      // Create activity log for renewal
      final user = await FirebaseAuthService().getUserData(
        FirebaseAuth.instance.currentUser!.uid,
      );
      if (user != null) {
        String description;
        if (widget.package == null ||
            _selectedPackageId != widget.package!.id) {
          description = (_isPaid ?? false)
              ? 'تم تجديد اشتراك ${widget.subscriber.fullName} وتغيير الباقة إلى ${_selectedPackage.name} (مدفوع)'
              : 'تم تجديد اشتراك ${widget.subscriber.fullName} وتغيير الباقة إلى ${_selectedPackage.name} (غير مدفوع)';
        } else {
          description = (_isPaid ?? false)
              ? 'تم تجديد اشتراك ${widget.subscriber.fullName} لمدة ${_selectedPackage.durationInDays} يوم (مدفوع)'
              : 'تم تجديد اشتراك ${widget.subscriber.fullName} لمدة ${_selectedPackage.durationInDays} يوم (غير مدفوع)';
        }

        final log = ActivityLogModel(
          adminId: widget.subscriber.adminId,
          id: DatabaseService().generateId(),
          subscriberId: widget.subscriber.id,
          userId: user.id,
          action: 'تجديد اشتراك',
          description: description,
          amount: (_isPaid ?? false)
              ? renewalAmount
              : 0.0, // Use null-aware operator
          timestamp: now,
        );
        // Save changes to database
        print('💾 حفظ بيانات المشترك...');
        await DatabaseService().updateSubscriberWithRenewal(
          updatedSubscriber,
          _selectedPackage.name,
          renewalAmount,
          (_isPaid ?? false),
        ); // Use null-aware operator
        print('✅ تم حفظ بيانات المشترك');

        print('📝 إضافة سجل النشاط...');
        await DatabaseService().addActivityLog(log);
        print('✅ تم إضافة سجل النشاط');

        // If paid, create payment record
        if (_isPaid ?? false) {
          // Use null-aware operator
          print('💰 إضافة سجل الدفع...');
          final paymentRecord = PaymentRecordModel(
            adminId: widget.subscriber.adminId,
            id: DatabaseService().generateId(),
            subscriberId: widget.subscriber.id,
            amount: renewalAmount,
            paymentMethod: _paymentMethod,
            notes: _notesController.text.trim(),
            paymentDate: now,
            recordedBy: user.id,
          );
          await DatabaseService().addPaymentRecord(paymentRecord);
          print('✅ تم إضافة سجل الدفع');
        }
        // SAS Radius Integration: Activate user upon renewal
        if (updatedSubscriber.username.isNotEmpty) {
          SasServerModel? sasServer;

          // Try to find a SAS server in this order:
          // 1. Server linked to this subscriber
          // 2. Currently connected server
          // 3. Any available active server
          if (updatedSubscriber.sasServerId != null) {
            final serverMap = await FirebaseService().getSasServerById(
              updatedSubscriber.sasServerId!,
            );
            if (serverMap != null) {
              sasServer = SasServerModel.fromMap(serverMap);
              print('Using subscriber-linked SAS server: ${sasServer.name}');
            }
          }

          if (sasServer == null) {
            // Try to use the currently connected server
            final connectedServerMap = await FirebaseService()
                .getConnectedSasServer();
            if (connectedServerMap != null) {
              sasServer = SasServerModel.fromMap(connectedServerMap);
              print('Using connected SAS server: ${sasServer.name}');

              // Link this subscriber to the connected server for future use
              updatedSubscriber = updatedSubscriber.copyWith(
                adminId: FirebaseAuth.instance.currentUser!.uid,
                sasServerId: sasServer.id,
              );
              await DatabaseService().updateSubscriber(updatedSubscriber, isSyncUpdate: false);
            }
          }

          if (_selectedPackage.sasProfileId != null) {
            try {
              final sasApiService = SasApiService(); // No parameters needed now
              final loggedIn = await sasApiService.login();
              if (loggedIn) {
                final profileId = int.tryParse(_selectedPackage.sasProfileId!);
                if (profileId != null) {
                  // التحقق من تغيير الباقة
                  bool packageChanged = false;
                  if (widget.package != null &&
                      widget.package!.sasProfileId != null) {
                    final currentProfileId = int.tryParse(
                      widget.package!.sasProfileId!,
                    );
                    packageChanged = currentProfileId != profileId;
                  }
                  Map<String, dynamic>? sasResponse;
                  if (packageChanged) {
                    // إذا تغيرت الباقة، استخدم API تغيير الباقة أولاً ثم التفعيل
                    print(
                      'Package changed, using changeProfile API then activation API',
                    );

                    // الخطوة الأولى: تغيير الباقة
                    final profileChangeResponse = await sasApiService
                        .changeUserProfile(
                          username: updatedSubscriber.username,
                          newProfileId: profileId,
                          method: (_isPaid ?? false)
                              ? 'credit'
                              : 'reward_points', // Use null-aware operator
                          comments:
                              'تغيير باقة من تطبيق ISP Manager - ${updatedSubscriber.fullName} من ${widget.package?.name ?? 'غير محدد'} إلى ${_selectedPackage.name}',
                        );

                    // الخطوة الثانية: تفعيل الاشتراك بالباقة الجديدة
                    if (profileChangeResponse != null &&
                        profileChangeResponse['status'] == 'success') {
                      print(
                        'Profile change successful, now activating subscription',
                      );
                      sasResponse = await sasApiService.activateUserInSas(
                        username: updatedSubscriber.username,
                        newProfileId: profileId,
                        comments:
                            'تفعيل اشتراك بعد تغيير الباقة من تطبيق ISP Manager - ${updatedSubscriber.fullName}',
                        moneyCollected: true,
                        issueInvoice: true,
                      );
                      print(
                        'SAS Activation Response after package change: $sasResponse',
                      );

                      // دمج معلومات تغيير الباقة مع نتيجة التفعيل
                      if (sasResponse != null &&
                          sasResponse['status'] == 'success') {
                        sasResponse['package_changed'] = true;
                        sasResponse['old_profile_id'] =
                            profileChangeResponse['old_profile_id'];
                        sasResponse['new_profile_id'] =
                            profileChangeResponse['new_profile_id'];
                      }
                    } else {
                      // إذا فشل تغيير الباقة، استخدم رد تغيير الباقة
                      sasResponse = profileChangeResponse;
                    }
                  } else {
                    // إذا لم تتغير الباقة، استخدم API التفعيل العادي

                    sasResponse = await sasApiService.activateUserInSas(
                      username: updatedSubscriber.username,
                      newProfileId: profileId,
                      comments:
                          'تجديد اشتراك من تطبيق ISP Manager - ${updatedSubscriber.fullName}',
                      moneyCollected: true,
                      issueInvoice: true,
                    );
                  }
                  if (sasResponse != null &&
                      sasResponse['status'] == 'success') {
                    if (sasResponse.containsKey('package_changed') &&
                        sasResponse['package_changed'] == true) {
                      if (sasResponse.containsKey('old_profile_id') &&
                          sasResponse.containsKey('new_profile_id')) {}
                    } else {}
                  } else if (sasResponse != null &&
                      sasResponse['status'] == 'info') {
                    // حالة المعلومات (مثل نفس الباقة)
                  } else {
                    if (sasResponse != null &&
                        sasResponse.containsKey('package_changed')) {
                      // حدث خطأ في التفعيل بعد تغيير الباقة بنجاح
                    } else {
                      // حدث خطأ في العملية الأساسية
                    }

                    if (sasResponse != null) {
                      if (sasResponse.containsKey('status_code')) {}
                    }
                  }
                } else {
                  print('⚠️ معرف البروفايل غير صالح في الباقة المختارة.');
                }
              } else {
                print(
                  '⚠️ فشل تسجيل الدخول إلى SAS Radius. يرجى التحقق من الإعدادات.',
                );
              }
            } catch (sasError) {
              print('⚠️ خطأ في الاتصال بـ SAS Radius: ${sasError.toString()}');
            }
          } else {
            print(
              '⚠️ الباقة المختارة لا تحتوي على معرف بروفايل SAS Radius. تم التجديد محلياً فقط.',
            );
          }
        }
      }

      // إرسال رسالة إذا تم تفعيل الخيار
      if (_sendMessage ?? false) {
        // Use null-aware operator
        try {
          final message = await MessageService().sendRenewalMessage(
            updatedSubscriber,
            _selectedPackage,
          );

          // فتح واتساب مع الرسالة
          WhatsAppService().sendMessage(updatedSubscriber.phoneNumber, message);
        } catch (_) {}
      }
      // طباعة الإيصال تلقائياً إذا تم تفعيل الخيار
      if (_printerSettings?.autoRenewalPrint == true && _printerEnabled) {
        try {
          await _printReceipt();
        } catch (_) {}
      }

      // تم الانتهاء من جميع العمليات بنجاح - سنرسل الرسالة في الصفحة الرئيسية

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      print('❌ خطأ في _processRenewal: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        try {
          _showSafeSnackBar(
            'خطأ في تجديد الاشتراك: ${e.toString()}',
            isError: true,
          );
        } catch (scaffoldError) {
          print('خطأ في تجديد الاشتراك: ${e.toString()}');
          // يمكن استخدام showDialog بدلاً من SnackBar
          try {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('خطأ'),
                content: Text('خطأ في تجديد الاشتراك: ${e.toString()}'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('موافق'),
                  ),
                ],
              ),
            );
          } catch (dialogError) {
            print('فشل في إظهار رسالة الخطأ: $dialogError');
          }
        }
      }
    }
  }

  String _getDeviceLinkTooltip() {
    if (_networkDevice != null) {
      return 'تعديل بيانات دخول الجهاز';
    } else if (_onlineInfo == null || _onlineInfo!['data'] == null || _onlineInfo!['data'].isEmpty) {
      return 'المشترك غير متصل حالياً';
    } else {
      final session = _onlineInfo!['data'][0];
      final ipAddress = session['framedipaddress']?.toString();
      return 'لا يوجد جهاز مطابق للـ IP: ${ipAddress ?? 'غير محدد'}';
    }
  }

  void _showNoDeviceMessage() {
    String message;
    String title;
    IconData icon;
    Color color;

    if (_onlineInfo == null || _onlineInfo!['data'] == null || _onlineInfo!['data'].isEmpty) {
      title = 'المشترك غير متصل';
      message = 'المشترك غير متصل حالياً. لا يمكن تحديد الجهاز الشبكي.';
      icon = Icons.wifi_off;
      color = Colors.orange;
    } else {
      final session = _onlineInfo!['data'][0];
      final ipAddress = session['framedipaddress']?.toString();
      title = 'لا يوجد جهاز مطابق';
      message = 'لا يوجد جهاز شبكي مطابق للـ IP: ${ipAddress ?? 'غير محدد'}\n\nيمكنك إضافة الجهاز من شاشة إدارة الأجهزة.';
      icon = Icons.router;
      color = Colors.red;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          if (_onlineInfo != null && _onlineInfo!['data'] != null && _onlineInfo!['data'].isNotEmpty)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                // يمكن إضافة navigation إلى شاشة إدارة الأجهزة هنا
              },
              icon: const Icon(Icons.add, size: 18),
              label: const Text('إضافة جهاز'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }
}

class _PaymentDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final UserModel currentUser;

  const _PaymentDialog({required this.subscriber, required this.currentUser});

  @override
  State<_PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<_PaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _amountController = TextEditingController();
  late String _paymentMethod; // Changed to late
  bool _isLoading = false;
  bool _isPartialPayment = false;
  bool? _sendMessage; // Changed to nullable bool
  PrinterSettingsModel? _printerSettings;
  bool _printerEnabled = false;
  bool _printerLoading = true;

  final List<String> _paymentMethods = [
    'نقداً',
    'كاش آسيا',
    'زين كاش',
    'آسيا حوالة',
    'حوالة بنكية',
    'أخرى',
  ];
  @override
  void initState() {
    super.initState();
    _paymentMethod = _paymentMethods.first; // Initialize payment method
    _sendMessage = false; // Initialize nullable boolean

    // إذا كان هناك دين مستحق، استخدم قيمة الدين، وإلا استخدم صفر
    _amountController.text = widget.subscriber.debtAmount > 0
        ? widget.subscriber.debtAmount.toString()
        : '';
    _loadPrinterSettings();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: isError ? Colors.red : Colors.green,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _loadPrinterSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final json = prefs.getString('printer_settings');
    if (json != null) {
      final settings = PrinterSettingsModel.fromJson(jsonDecode(json));
      setState(() {
        _printerSettings = settings;
        _printerEnabled =
            settings.connectedDeviceAddress != null &&
            settings.connectedDeviceAddress!.isNotEmpty;
        _printerLoading = false;
      });
    } else {
      setState(() {
        _printerSettings = null;
        _printerEnabled = false;
        _printerLoading = false;
      });
    }
  }

  Future<void> _printReceipt() async {
    if (_printerSettings == null) return;

    try {
      // إظهار رسالة تحميل فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('جاري الطباعة...')));
      }

      final now = DateTime.now();
      final paymentAmount = double.tryParse(_amountController.text) ?? 0.0;
      final data = {
        'subscriberName': widget.subscriber.fullName,
        'subscriptionNumber': widget.subscriber.id,
        'dateTime':
            '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour}:${now.minute.toString().padLeft(2, '0')}',
        'paymentAmount': paymentAmount.toInt(),
        'operationType': 'تسديد دين',
        'employeeName': '',
        'companyInfo': _printerSettings?.companyName ?? '',
      };

      // طباعة الإيصال بالصورة (حل مشاكل التشفير العربي)
      await PrinterService.printReceiptUnified(
        settings: _printerSettings!,
        data: data,
        operationType: 'تسديد',
        asImage: true, // استخدام الطباعة بالصورة دائماً
      );
      // رسالة نجاح فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        _showSafeSnackBar('تم طباعة إيصال الدفع بنجاح');
      }
    } catch (e) {
      // رسالة خطأ فقط إذا كان الwidget مازال موجوداً
      if (mounted) {
        _showSafeSnackBar('خطأ في الطباعة: $e', isError: true);
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // الحصول على المبلغ المدفوع من حقل الإدخال
      final paymentAmount = double.tryParse(_amountController.text) ?? 0.0;

      // التحقق من أن المبلغ المدفوع أكبر من صفر
      if (paymentAmount <= 0) {
        setState(() => _isLoading = false);
        _showSafeSnackBar(
          'يجب أن يكون المبلغ المدفوع أكبر من صفر',
          isError: true,
        );
        return;
      }

      // التحقق من أن المبلغ المدفوع لا يتجاوز الدين المستحق فقط إذا كان هناك دين
      if (widget.subscriber.debtAmount > 0 &&
          paymentAmount > widget.subscriber.debtAmount) {
        setState(() => _isLoading = false);
        _showSafeSnackBar(
          'المبلغ المدفوع أكبر من الدين المستحق',
          isError: true,
        );
        return;
      }

      final now = DateTime.now();

      // حساب الدين المتبقي بعد الدفع
      final remainingDebt = widget.subscriber.debtAmount - paymentAmount;

      // تحديد حالة الدفع بناءً على الدين المتبقي
      final newPaymentStatus = remainingDebt <= 0
          ? PaymentStatus.paid
          : PaymentStatus.pending;

      // إنشاء سجل الدفعة
      final paymentRecord = PaymentRecordModel(
        adminId: widget.subscriber.adminId,
        id: _firestore.collection('paymentRecord').doc().id,
        subscriberId: widget.subscriber.id,
        amount: paymentAmount,
        paymentMethod: _paymentMethod,
        notes: _notesController.text.trim(),
        paymentDate: now,
        recordedBy: widget.currentUser.id,
      );

      // إنشاء سجل النشاط
      final activityLog = ActivityLogModel(
        adminId: widget.subscriber.adminId,
        id: _firestore.collection('activityLogs').doc().id,
        subscriberId: widget.subscriber.id,
        userId: widget.currentUser.id,
        action: 'تسجيل دفعة',
        description: remainingDebt > 0
            ? 'تم تسجيل دفعة جزئية بمبلغ ${await AppSettingsService.formatCurrency(paymentAmount)} عبر $_paymentMethod (متبقي ${await AppSettingsService.formatCurrency(remainingDebt)})'
            : 'تم تسديد كامل الدين بمبلغ ${await AppSettingsService.formatCurrency(paymentAmount)} عبر $_paymentMethod',
        amount: paymentAmount,
        timestamp: now,
      );

      // تحديث المشترك
      final updatedSubscriber = widget.subscriber.copyWith(
        adminId: FirebaseAuth.instance.currentUser!.uid,
        debtAmount: remainingDebt,
        paymentStatus: newPaymentStatus,
      );
      // حفظ جميع التحديثات
      await DatabaseService().updateSubscriberWithPayment(
        updatedSubscriber,
        paymentAmount,
        _paymentMethod,
        _notesController.text.trim(),
      );
      await DatabaseService().addPaymentRecord(paymentRecord);
      await DatabaseService().addActivityLog(activityLog);

      // إرسال رسالة إذا تم تفعيل الخيار
      if (_sendMessage ?? false) {
        // Use null-aware operator
        try {
          final package = await DatabaseService().getPackageById(
            updatedSubscriber.packageId,
          );
          if (package == null) {
            throw Exception('لم يتم العثور على الباقة');
          }

          final message = await MessageService().sendPaymentMessage(
            updatedSubscriber,
            package,
            paymentAmount,
          );
          // فتح واتساب مع الرسالة
          WhatsAppService().sendMessage(updatedSubscriber.phoneNumber, message);
        } catch (msgError) {
          // إذا فشل إرسال الرسالة، نعرض خطأ ولكن نستمر في العملية
          _showSafeSnackBar(
            'تم تسجيل الدفعة ولكن فشل إرسال الرسالة: ${msgError.toString()}',
            isError: true,
          );
        }
      }

      // طباعة الإيصال تلقائياً إذا تم تفعيل الخيار
      if (_printerSettings?.autoPaymentPrint == true && _printerEnabled) {
        try {
          await _printReceipt();
        } catch (printError) {
          _showSafeSnackBar(
            'تم تسجيل الدفعة ولكن فشلت الطباعة التلقائية: ${printError.toString()}',
            isError: true,
          );
        }
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      _showSafeSnackBar('خطأ في تسجيل الدفعة: ${e.toString()}', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تسجيل دفعة'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المشترك: ${widget.subscriber.fullName}'),
                const SizedBox(height: 8),
                FutureBuilder<String>(
                  future: widget.subscriber.debtAmount > 0
                      ? AppSettingsService.formatCurrency(
                          widget.subscriber.debtAmount,
                        ).then((amount) => 'المبلغ المستحق: $amount')
                      : widget.subscriber.debtAmount < 0
                      ? AppSettingsService.formatCurrency(
                          widget.subscriber.advancePaymentAmount,
                        ).then((amount) => 'الرصيد المقدم: $amount')
                      : Future.value(
                          'لا توجد ديون مستحقة - يمكنك إضافة رصيد مقدم',
                        ),
                  builder: (context, snapshot) {
                    return Text(snapshot.data ?? '...');
                  },
                ),
                const SizedBox(height: 16),
                // Switch for partial payment - إظهاره فقط إذا كان هناك دين مستحق
                if (widget.subscriber.debtAmount > 0) ...[
                  SwitchListTile(
                    title: const Text('دفع جزئي'),
                    subtitle: const Text('تمكين الدفع الجزئي للديون المستحقة'),
                    value: _isPartialPayment,
                    onChanged: (value) {
                      setState(() {
                        _isPartialPayment = value;
                        if (!value) {
                          // إذا تم إلغاء الدفع الجزئي، أعد تعيين المبلغ إلى إجمالي الدين
                          _amountController.text = widget.subscriber.debtAmount
                              .toString();
                        }
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
                // Payment amount field
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ المدفوع',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  enabled:
                      widget.subscriber.debtAmount <= 0 ||
                      _isPartialPayment, // تمكين التعديل إذا لم يكن هناك دين أو في حالة الدفع الجزئي
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ المدفوع';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'يرجى إدخال رقم صحيح';
                    }
                    if (amount <= 0) {
                      return 'يجب أن يكون المبلغ أكبر من صفر';
                    }
                    // التحقق من الحد الأقصى فقط إذا كان هناك دين مستحق
                    if (widget.subscriber.debtAmount > 0 &&
                        amount > widget.subscriber.debtAmount) {
                      return 'المبلغ أكبر من الدين المستحق';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Payment method dropdown
                DropdownButtonFormField<String>(
                  value: _paymentMethod,
                  decoration: const InputDecoration(
                    labelText: 'طريقة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  items: _paymentMethods.map((method) {
                    return DropdownMenuItem(value: method, child: Text(method));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _paymentMethod = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Notes field
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),

                // إضافة خيار إرسال رسالة
                const SizedBox(height: 12),
                SwitchListTile(
                  title: const Text('إرسال رسالة تأكيد الدفع'),
                  subtitle: const Text('إرسال رسالة للمشترك بعد تسجيل الدفعة'),
                  value: _sendMessage ?? false, // Use null-aware operator
                  onChanged: (value) {
                    setState(() {
                      _sendMessage = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.secondary,
                  secondary: Icon(
                    Icons.message,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        if (!_printerLoading && _printerEnabled)
          OutlinedButton.icon(
            onPressed: _isLoading ? null : _printReceipt,
            icon: const Icon(Icons.print),
            label: const Text('طباعة الشريط'),
          ),
        ElevatedButton(
          onPressed: _isLoading ? null : _processPayment,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('تسجيل'),
        ),
      ],
    );
  }
}

class _PaymentReminderDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final PackageModel package;

  const _PaymentReminderDialog({
    required this.subscriber,
    required this.package,
  });

  @override
  State<_PaymentReminderDialog> createState() => _PaymentReminderDialogState();
}

class _PaymentReminderDialogState extends State<_PaymentReminderDialog> {
  List<String> _reminderMessages = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReminderMessages();
  }

  Future<void> _loadReminderMessages() async {
    setState(() => _isLoading = true);

    try {
      // استخدام خدمة الذكاء الاصطناعي لإنشاء رسائل تذكير
      _reminderMessages = await AIService().generatePaymentReminders(
        widget.subscriber,
        widget.package,
      );
    } catch (e) {
      // في حالة حدوث خطأ، استخدم رسائل افتراضية
      final currencyAmount = await AppSettingsService.formatCurrency(
        widget.subscriber.debtAmount,
      );
      _reminderMessages = [
        'عزيزي ${widget.subscriber.fullName}، نذكركم بضرورة تسديد رسوم الاشتراك البالغة $currencyAmount.',
        'السيد/ة ${widget.subscriber.fullName}، يرجى تسديد المبلغ المستحق قدره $currencyAmount لتجنب قطع الخدمة.',
        'تحذير نهائي: سيتم إيقاف خدمة الإنترنت في حال عدم تسديد المبلغ المستحق خلال 24 ساعة.',
      ];
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _sendWhatsAppMessage(String message) async {
    try {
      await WhatsAppService().sendMessage(
        widget.subscriber.phoneNumber,
        message,
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح واتساب. تأكد من تثبيت التطبيق'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.message, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8),
          const Text('رسائل التذكير'),
        ],
      ),
      content: _isLoading
          ? const SizedBox(
              height: 100,
              child: Center(child: CircularProgressIndicator()),
            )
          : SizedBox(
              width: double.maxFinite,
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _reminderMessages.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final message = _reminderMessages[index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'رسالة ${index + 1}:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(message),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              // نسخ الرسالة إلى الحافظة
                              Clipboard.setData(ClipboardData(text: message));
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم نسخ الرسالة'),
                                  duration: Duration(seconds: 1),
                                ),
                              );
                            },
                            icon: const Icon(Icons.copy, size: 16),
                            label: const Text('نسخ'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: () => _sendWhatsAppMessage(message),
                            icon: const Icon(Icons.send, size: 16),
                            label: const Text('إرسال عبر واتساب'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(
                                0xFF25D366,
                              ), // لون واتساب
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }
}

class _EditSubscriberDialog extends StatefulWidget {
  final SubscriberModel subscriber;

  const _EditSubscriberDialog({required this.subscriber});

  @override
  State<_EditSubscriberDialog> createState() => _EditSubscriberDialogState();
}

class _EditSubscriberDialogState extends State<_EditSubscriberDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _macController;
  late TextEditingController _routerController;
  late TextEditingController _notesController;
  late TextEditingController _usernameController;
  late TextEditingController _passwordController;
  bool _isActive = true;
  bool _isLoading = false;
  DateTime? _subscriptionStart;
  DateTime? _subscriptionEnd;
  late SubscriptionType _subscriptionType;
  List<SasServerModel> _sasServers = [];
  String? _selectedSasServerId;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.subscriber.fullName);
    _phoneController = TextEditingController(
      text: widget.subscriber.phoneNumber,
    );
    _addressController = TextEditingController(text: widget.subscriber.address);
    _macController = TextEditingController(text: widget.subscriber.macAddress);
    _routerController = TextEditingController(
      text: widget.subscriber.routerName,
    );
    _notesController = TextEditingController(
      text: widget.subscriber.technicalNotes,
    );
    _usernameController = TextEditingController(
      text: widget.subscriber.username,
    );
    _passwordController = TextEditingController(
      text: widget.subscriber.password,
    );
    _isActive = widget.subscriber.isActive;
    _subscriptionStart = widget.subscriber.subscriptionStart;
    _subscriptionEnd = widget.subscriber.subscriptionEnd;
    _subscriptionType = widget.subscriber.subscriptionType;
    _selectedSasServerId = widget.subscriber.sasServerId;
    _loadSasServers();
  }

  Future<void> _loadSasServers() async {
    try {
      final servers = await FirebaseService().getSasServers();
      setState(() {
        _sasServers = servers
            .map((map) => SasServerModel.fromMap(map))
            .toList();
      });
    } catch (e) {
      print('Error loading SAS servers: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _macController.dispose();
    _routerController.dispose();
    _notesController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _pickStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _subscriptionStart ?? DateTime.now(),
      firstDate: DateTime(
        DateTime.now().year - 5,
      ), // Allow picking dates up to 5 years in the past
      lastDate: DateTime(
        DateTime.now().year + 5,
      ), // Allow picking dates up to 5 years in the future
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).dialogBackgroundColor,
              onSurface: Theme.of(context).colorScheme.onSurface,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _subscriptionStart = picked;
        // Ensure end date is not before start date if both are set
        if (_subscriptionEnd != null &&
            _subscriptionEnd!.isBefore(_subscriptionStart!)) {
          _subscriptionEnd = _subscriptionStart;
        }
      });
    }
  }

  Future<void> _pickEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _subscriptionEnd ?? _subscriptionStart ?? DateTime.now(),
      firstDate:
          _subscriptionStart ??
          DateTime(DateTime.now().year - 5), // Cannot be before start date
      lastDate: DateTime(
        DateTime.now().year + 5,
      ), // Allow picking dates up to 5 years in the future
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).dialogBackgroundColor,
              onSurface: Theme.of(context).colorScheme.onSurface,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _subscriptionEnd = picked;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);
    try {
      final updatedSubscriber = widget.subscriber.copyWith(
        adminId: widget.subscriber.adminId,
        fullName: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        macAddress: _macController.text.trim(),
        routerName: _routerController.text.trim(),
        technicalNotes: _notesController.text.trim(),
        isActive: _isActive,
        subscriptionStart: _subscriptionStart,
        subscriptionEnd: _subscriptionEnd,
        subscriptionType: _subscriptionType,
        username: _usernameController.text.trim(),
        password: _passwordController.text.trim(),
        sasServerId: _selectedSasServerId, // Save the selected SAS server ID
      );

      // إنشاء سجل نشاط للتعديل
      final user = await FirebaseAuthService().getUserData(
        FirebaseAuth.instance.currentUser!.uid,
      );
      if (user != null) {
        final now = DateTime.now();
        final activityLog = ActivityLogModel(
          adminId: widget.subscriber.adminId,
          id: DatabaseService().generateId(),
          subscriberId: widget.subscriber.id,
          userId: user.id,
          action: 'تعديل بيانات',
          description: 'تم تعديل بيانات المشترك ${widget.subscriber.fullName}',
          amount: 0.0,
          timestamp: now,
        );
        // حفظ التغييرات في قاعدة البيانات
        await DatabaseService().updateSubscriberData(
          updatedSubscriber,
          widget.subscriber,
        );
        await DatabaseService().addActivityLog(activityLog);
      }

      setState(() => _isLoading = false);
      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تعديل بيانات المشترك: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل المشترك'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الاسم الكامل
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم الكامل',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم المشترك';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // رقم الهاتف
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال رقم الهاتف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // العنوان
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال العنوان';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // نوع الاشتراك
              DropdownButtonFormField<SubscriptionType>(
                value: _subscriptionType,
                decoration: const InputDecoration(
                  labelText: 'نوع الاشتراك',
                  border: OutlineInputBorder(),
                ),
                items: SubscriptionType.values.map((type) {
                  String text;
                  switch (type) {
                    case SubscriptionType.broadband:
                      text = 'برودباند';
                      break;
                    case SubscriptionType.hotspot:
                      text = 'هوت سبوت';
                      break;
                  }
                  return DropdownMenuItem(value: type, child: Text(text));
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _subscriptionType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // اسم المستخدم وكلمة المرور
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المستخدم',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: false,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // عنوان MAC
              TextFormField(
                controller: _macController,
                decoration: const InputDecoration(
                  labelText: 'عنوان MAC (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // اسم الراوتر
              TextFormField(
                controller: _routerController,
                decoration: const InputDecoration(
                  labelText: 'اسم الراوتر (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // الملاحظات الفنية
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'الملاحظات الفنية (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // حالة النشاط
              SwitchListTile(
                title: const Text('نشط'),
                subtitle: const Text('تحديد ما إذا كان المشترك نشطًا أم لا'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                activeColor: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              // --- حقل اختيار تاريخ البداية ---
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: _pickStartDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ بداية الاشتراك',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _SubscriberDetailPageState._formatDate(
                            _subscriptionStart,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // --- حقل اختيار تاريخ النهاية ---
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: _pickEndDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ نهاية الاشتراك',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _SubscriberDetailPageState._formatDate(
                            _subscriptionEnd,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // SAS Server selection
              if (_sasServers.isNotEmpty)
                DropdownButtonFormField<String>(
                  value:
                      (_sasServers
                              .where(
                                (server) => server.id == _selectedSasServerId,
                              )
                              .length ==
                          1)
                      ? _selectedSasServerId
                      : null,
                  decoration: const InputDecoration(
                    labelText: 'خادم SAS Radius (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('لا يوجد خادم SAS'),
                    ),
                    ..._sasServers
                        .where(
                          (server) => server.id != null,
                        ) // Filter out servers with null IDs
                        .toSet() // Remove duplicates based on server object equality
                        .map((server) {
                          return DropdownMenuItem(
                            value: server.id,
                            child: Text(server.name),
                          );
                        }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedSasServerId = value;
                    });
                  },
                ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveChanges,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }
}

class _PreviousDebtDialog extends StatefulWidget {
  final SubscriberModel subscriber;
  final UserModel currentUser;

  const _PreviousDebtDialog({
    required this.subscriber,
    required this.currentUser,
  });

  @override
  State<_PreviousDebtDialog> createState() => _PreviousDebtDialogState();
}

class _PreviousDebtDialogState extends State<_PreviousDebtDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isLoading = false;
  bool? _sendMessage; // Changed to nullable bool

  @override
  void initState() {
    super.initState();
    _sendMessage = false; // Initialize nullable boolean
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _sendWhatsAppMessage(
    SubscriberModel subscriber,
    String message,
  ) async {
    try {
      await WhatsAppService().sendMessage(subscriber.phoneNumber, message);
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح واتساب. تأكد من تثبيت التطبيق'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _addPreviousDebt() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final debtAmount = double.tryParse(_amountController.text) ?? 0.0;

      if (debtAmount <= 0) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يجب أن يكون مبلغ الدين أكبر من صفر')),
        );
        return;
      }

      final now = DateTime.now();

      // حساب إجمالي الدين الجديد (الدين الحالي + الدين السابق)
      final newTotalDebt = widget.subscriber.debtAmount + debtAmount;

      // تحديث المشترك بإضافة الدين السابق
      final updatedSubscriber = widget.subscriber.copyWith(
        adminId: FirebaseAuth.instance.currentUser!.uid,
        debtAmount: newTotalDebt,
        paymentStatus: PaymentStatus.pending,
      );
      final FirebaseFirestore firestore = FirebaseFirestore.instance;
      // إنشاء سجل نشاط لإضافة الدين السابق
      final activityLog = ActivityLogModel(
        adminId: widget.subscriber.adminId,
        id: firestore.collection('activityLog').doc().id,
        subscriberId: widget.subscriber.id,
        userId: widget.currentUser.id,
        action: 'إضافة دين سابق',
        description:
            'تمت إضافة دين سابق بمبلغ ${await AppSettingsService.formatCurrency(debtAmount)}. ${_notesController.text.isNotEmpty ? 'ملاحظات: ${_notesController.text}' : ''}',
        amount: debtAmount,
        timestamp: now,
      );

      // حفظ التغييرات في قاعدة البيانات
      await DatabaseService().updateSubscriberWithDebt(
        updatedSubscriber,
        debtAmount,
        _notesController.text.trim(),
      );
      await DatabaseService().addActivityLog(activityLog);

      // إرسال رسالة إذا تم تفعيل الخيار
      if (_sendMessage ?? false) {
        // Use null-aware operator
        try {
          final package = await DatabaseService().getPackageById(
            updatedSubscriber.packageId,
          );
          if (package == null) {
            throw Exception('لم يتم العثور على الباقة');
          }

          final message = await MessageService().sendReminderMessage(
            updatedSubscriber,
            package,
          );

          // فتح واتساب مع الرسالة
          _sendWhatsAppMessage(updatedSubscriber, message);
        } catch (msgError) {
          // إذا فشل إرسال الرسالة، نعرض خطأ ولكن نستمر في العملية
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة الدين السابق ولكن فشل إرسال الرسالة: ${msgError.toString()}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إضافة الدين السابق: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة دين سابق'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المشترك: ${widget.subscriber.fullName}'),
                const SizedBox(height: 8),
                FutureBuilder<String>(
                  future: AppSettingsService.formatCurrency(
                    widget.subscriber.debtAmount,
                  ),
                  builder: (context, snapshot) {
                    return Text('الدين الحالي: ${snapshot.data ?? '...'}');
                  },
                ),
                const SizedBox(height: 16),

                // مبلغ الدين السابق
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'مبلغ الدين السابق',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال مبلغ الدين';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'يرجى إدخال رقم صحيح';
                    }
                    if (amount <= 0) {
                      return 'يجب أن يكون المبلغ أكبر من صفر';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // ملاحظات
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    border: OutlineInputBorder(),
                    hintText: 'سبب إضافة الدين السابق',
                  ),
                  maxLines: 2,
                ),

                // إضافة خيار إرسال رسالة
                const SizedBox(height: 12),
                SwitchListTile(
                  title: const Text('إرسال رسالة تذكير'),
                  subtitle: const Text('إرسال رسالة للمشترك بعد إضافة الدين'),
                  value: _sendMessage ?? false, // Use null-aware operator
                  onChanged: (value) {
                    setState(() {
                      _sendMessage = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.secondary,
                  secondary: Icon(
                    Icons.message,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addPreviousDebt,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إضافة'),
        ),
      ],
    );
  }
}

// دالة لتحويل مدة الجلسة من ثواني إلى نص منسق
String formatDuration(int seconds) {
  final days = seconds ~/ 86400;
  final hours = (seconds % 86400) ~/ 3600;
  final minutes = (seconds % 3600) ~/ 60;
  final secs = seconds % 60;
  final parts = <String>[];
  if (days > 0) parts.add('$days يوم');
  if (hours > 0) parts.add('$hours ساعة');
  if (minutes > 0) parts.add('$minutes دقيقة');
  if (secs > 0 || parts.isEmpty) parts.add('$secs ثانية');
  return parts.join(' ');
}

// دالة لتحويل البايتات إلى صيغة مقروءة
String formatBytes(dynamic value) {
  if (value == null) return '-';
  int bytes = 0;
  try {
    bytes = int.parse(value.toString());
  } catch (_) {
    return value.toString();
  }
  if (bytes < 1024) return '$bytes بايت';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
  if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
}

  String _getDeviceLinkTooltip() {
    if (_networkDevice != null) {
      return 'تعديل بيانات دخول الجهاز';
    } else if (_onlineInfo == null || _onlineInfo!['data'] == null || _onlineInfo!['data'].isEmpty) {
      return 'المشترك غير متصل حالياً';
    } else {
      final session = _onlineInfo!['data'][0];
      final ipAddress = session['framedipaddress']?.toString();
      return 'لا يوجد جهاز مطابق للـ IP: ${ipAddress ?? 'غير محدد'}';
    }
  }

  void _showNoDeviceMessage() {
    String message;
    String title;
    IconData icon;
    Color color;

    if (_onlineInfo == null || _onlineInfo!['data'] == null || _onlineInfo!['data'].isEmpty) {
      title = 'المشترك غير متصل';
      message = 'المشترك غير متصل حالياً. لا يمكن تحديد الجهاز الشبكي.';
      icon = Icons.wifi_off;
      color = Colors.orange;
    } else {
      final session = _onlineInfo!['data'][0];
      final ipAddress = session['framedipaddress']?.toString();
      title = 'لا يوجد جهاز مطابق';
      message = 'لا يوجد جهاز شبكي مطابق للـ IP: ${ipAddress ?? 'غير محدد'}\n\nيمكنك إضافة الجهاز من شاشة إدارة الأجهزة.';
      icon = Icons.router;
      color = Colors.red;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          if (_onlineInfo != null && _onlineInfo!['data'] != null && _onlineInfo!['data'].isNotEmpty)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                // يمكن إضافة navigation إلى شاشة إدارة الأجهزة هنا
              },
              icon: const Icon(Icons.add, size: 18),
              label: const Text('إضافة جهاز'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

class _EditDeviceCredentialsDialog extends StatefulWidget {
  final NetworkDevice device;
  final String currentUsername;
  final String currentPassword;

  const _EditDeviceCredentialsDialog({
    required this.device,
    required this.currentUsername,
    required this.currentPassword,
  });

  @override
  State<_EditDeviceCredentialsDialog> createState() => _EditDeviceCredentialsDialogState();
}

class _EditDeviceCredentialsDialogState extends State<_EditDeviceCredentialsDialog> {
  late TextEditingController _usernameController;
  late TextEditingController _passwordController;
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(text: widget.currentUsername);
    _passwordController = TextEditingController(text: widget.currentPassword);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.settings, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8),
          const Text('تعديل بيانات دخول الجهاز'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الجهاز
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات الجهاز:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text('الاسم: ${widget.device.name}'),
                  Text('IP: ${widget.device.ipAddress}'),
                  Text('النوع: ${widget.device.type}'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // حقل اسم المستخدم
            TextField(
              controller: _usernameController,
              decoration: const InputDecoration(
                labelText: 'اسم المستخدم',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // حقل كلمة المرور
            TextField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),

            // ملاحظة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange.shade700, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم حفظ هذه البيانات كبيانات دخول خاصة لهذا الجهاز وإعادة جلب معلومات الإشارة',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCredentials,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }

  void _saveCredentials() {
    final username = _usernameController.text.trim();
    final password = _passwordController.text.trim();

    if (username.isEmpty || password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى ملء جميع الحقول')),
      );
      return;
    }

    Navigator.of(context).pop({
      'username': username,
      'password': password,
    });
  }
}
