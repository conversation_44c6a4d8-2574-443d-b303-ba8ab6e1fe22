import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:isp_manager/services/database_service.dart';
import '../models/sas_server_model.dart';
import '../services/sqlite_service.dart';

class AddEditSasServerPage extends StatefulWidget {
  final SasServerModel? server;

  const AddEditSasServerPage({super.key, this.server});

  @override
  State<AddEditSasServerPage> createState() => _AddEditSasServerPageState();
}

class _AddEditSasServerPageState extends State<AddEditSasServerPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _hostController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final SQLiteService _sqliteService = SQLiteService();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    if (widget.server != null) {
      _nameController.text = widget.server!.name;
      _hostController.text = widget.server!.host;
      _usernameController.text = widget.server!.username;
      _passwordController.text = widget.server!.password;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _hostController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _saveServer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final newServer = SasServerModel(
        adminId: DatabaseService().adminId,
        id: widget.server?.id,
        name: _nameController.text.trim(),
        host: _hostController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
        isConnected: widget.server?.isConnected ?? false,
      );
      final FirebaseFirestore firestore = FirebaseFirestore.instance;

      if (widget.server == null) {
        try {
          SasServerModel newSas = newServer.copyWith(
            adminId: DatabaseService().adminId,
            id: firestore.collection('sas_servers').doc().id,
          );
          await FirebaseFirestore.instance
              .collection('sas_servers')
              .doc(newSas.id) // لازم الـ id يكون مضبوط
              .set(newSas.toMap());
        } catch (e) {
          print('Error updating package: $e');
        }
        // await _sqliteService.insertSasServer(newServer.toMap());
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('SAS server added successfully!')),
        );
      } else {
        SasServerModel newSas = newServer.copyWith(
          adminId: DatabaseService().adminId,
          id: newServer.id,
        );
        await FirebaseFirestore.instance
            .collection('sas_servers')
            .doc(newSas.id) // لازم الـ id يكون مضبوط
            .set(newSas.toMap());
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('SAS server updated successfully!')),
        );
      }
      Navigator.pop(context, true); // Indicate success
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to save SAS server: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.server == null ? 'Add SAS Server' : 'Edit SAS Server',
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Server Name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter a server name'
                          : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _hostController,
                      decoration: const InputDecoration(
                        labelText: 'Host (e.g., demo4.sasradius.com)',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter the host'
                          : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter the username'
                          : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                      validator: (v) => v == null || v.isEmpty
                          ? 'Please enter the password'
                          : null,
                    ),
                    const SizedBox(height: 24),
                    if (_errorMessage != null)
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ElevatedButton(
                      onPressed: _saveServer,
                      child: Text(
                        widget.server == null ? 'Add Server' : 'Update Server',
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
