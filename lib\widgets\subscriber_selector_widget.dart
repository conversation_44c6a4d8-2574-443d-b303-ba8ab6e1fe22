import 'package:flutter/material.dart';
import '../models/subscriber_model.dart';
import '../services/database_service.dart';

class SubscriberSelectorWidget extends StatefulWidget {
  final TextEditingController nameController;
  final VoidCallback? onSubscriberSelected;

  const SubscriberSelectorWidget({
    super.key,
    required this.nameController,
    this.onSubscriberSelected,
  });

  @override
  State<SubscriberSelectorWidget> createState() => _SubscriberSelectorWidgetState();
}

class _SubscriberSelectorWidgetState extends State<SubscriberSelectorWidget> {
  List<SubscriberModel> _subscribers = [];
  List<SubscriberModel> _filteredSubscribers = [];
  bool _isLoading = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSubscribers();
    _searchController.addListener(_filterSubscribers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSubscribers() async {
    setState(() => _isLoading = true);
    try {
      final subscribers = await DatabaseService().getSubscribers();
      setState(() {
        _subscribers = subscribers;
        _filteredSubscribers = subscribers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المشتركين: $e')),
        );
      }
    }
  }

  void _filterSubscribers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredSubscribers = _subscribers.where((subscriber) {
        return subscriber.fullName.toLowerCase().contains(query) ||
               subscriber.phoneNumber.toLowerCase().contains(query) ||
               subscriber.username.toLowerCase().contains(query);
      }).toList();
    });
  }

  void _selectSubscriber(SubscriberModel subscriber) {
    widget.nameController.text = subscriber.fullName;
    Navigator.pop(context);
    widget.onSubscriberSelected?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Text(
                  'اختيار مشترك',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Search Field
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: 'البحث في المشتركين',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterSubscribers();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Subscribers List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredSubscribers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _subscribers.isEmpty
                                  ? 'لا يوجد مشتركين مسجلين'
                                  : 'لا يوجد نتائج للبحث',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: _filteredSubscribers.length,
                        itemBuilder: (context, index) {
                          final subscriber = _filteredSubscribers[index];
                          return _buildSubscriberCard(subscriber);
                        },
                      ),
          ),

          // Manual Entry Button
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.edit),
            label: const Text('إدخال اسم يدوياً'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriberCard(SubscriberModel subscriber) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _selectSubscriber(subscriber),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
                child: Text(
                  subscriber.fullName.isNotEmpty 
                      ? subscriber.fullName[0].toUpperCase()
                      : 'س',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Subscriber Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subscriber.fullName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subscriber.phoneNumber,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    if (subscriber.username.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Text(
                        'اسم المستخدم: ${subscriber.username}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Status Indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: subscriber.isActive && !subscriber.isExpired
                      ? Colors.green
                      : Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  subscriber.isActive && !subscriber.isExpired ? 'نشط' : 'منتهي',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
