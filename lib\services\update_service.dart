import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:android_intent_plus/android_intent.dart';

class UpdateService {
  final SupabaseClient supabase = SupabaseClient(
    'https://hflualxkcpsyjkujhnql.supabase.co',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhmbHVhbHhrY3BzeWprdWpobnFsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzEyMzIsImV4cCI6MjA2NDYwNzIzMn0.2t77DlL4UnaW-Ls8tFA8OKkURI9Rb2kpwt-4xsJHNbk',
  );  Future<Map<String, dynamic>?> fetchLatestVersion() async {
    try {
      print('تحقق من إعدادات التحديث من قاعدة البيانات...');
      
      // التحقق أولاً من وجود الجدول والسجلات
      final countResponse = await supabase
          .from('app_versions')
          .select('count')
          .count(CountOption.exact);
      
      print('عدد السجلات في الجدول: ${countResponse.count}');
      
      if (countResponse.count == 0) {
        print('تحذير: الجدول فارغ - لا توجد إعدادات للتحديث');
        return null;
      }

      // جلب جميع السجلات وترتيبها
      final allRecords = await supabase
          .from('app_versions')
          .select('version, telegram_link, force_update, created_at')
          .order('created_at', ascending: false);

      print('جميع السجلات: $allRecords');

      // تصفية السجلات ذات التنسيق الصحيح فقط
      List<Map<String, dynamic>> validRecords = [];
      
      for (var record in allRecords) {
        final String version = record['version'];
        if (_isValidVersionFormat(version)) {
          validRecords.add(record);
        } else {
          print('تخطي سجل بتنسيق إصدار غير صحيح: $version');
        }
      }

      if (validRecords.isEmpty) {
        print('لم يتم العثور على أي سجل بتنسيق إصدار صحيح');
        return null;
      }

      // ترتيب السجلات الصحيحة حسب الإصدار (الأحدث أولاً)
      validRecords.sort((a, b) {
        try {
          return _compareVersions(a['version'], b['version']) ? -1 : 1;
        } catch (e) {
          return 0;
        }
      });

      // إرجاع أحدث سجل
      final latestRecord = validRecords.first;
      print('أحدث سجل صحيح: $latestRecord');
      return latestRecord;
    } catch (e) {
      print('خطأ في جلب إعدادات التحديث: $e');
      
      // التحقق من نوع الخطأ وتقديم حلول مقترحة
      if (e.toString().contains('PGRST116')) {
        print('❌ حل مقترح: الجدول فارغ - يرجى إضافة سجلات في جدول app_versions');
      } else if (e.toString().contains('relation "app_versions" does not exist')) {
        print('❌ حل مقترح: الجدول غير موجود - يرجى إنشاء جدول app_versions');
      } else if (e.toString().contains('permission')) {
        print('❌ حل مقترح: مشكلة في الصلاحيات - يرجى تفعيل RLS policies');
      }
      
      return null;
    }
  }Future<bool> isUpdateRequired() async {
    try {
      print('بدء فحص التحديث...');
      final latestVersionData = await fetchLatestVersion();
      if (latestVersionData == null) {
        print('لم يتم العثور على بيانات التحديث في قاعدة البيانات');
        return false;
      }

      final String latestVersion = latestVersionData['version'];
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final String currentVersion = packageInfo.version;
      final bool forceUpdate = latestVersionData['force_update'] ?? false;

      print('الإصدار الحالي: $currentVersion');
      print('أحدث إصدار: $latestVersion');
      print('التحديث الإجباري: $forceUpdate');

      // التحقق من صحة تنسيق الإصدار
      if (!_isValidVersionFormat(latestVersion)) {
        print('تحذير: تنسيق الإصدار في قاعدة البيانات غير صحيح: $latestVersion');
        return false;
      }

      if (!_isValidVersionFormat(currentVersion)) {
        print('تحذير: تنسيق الإصدار الحالي غير صحيح: $currentVersion');
        return false;
      }

      // مقارنة الإصدارات أولاً
      final bool versionNeedsUpdate = _compareVersions(currentVersion, latestVersion);
      
      print('نتيجة مقارنة الإصدارات: $versionNeedsUpdate');

      // إذا كان الإصدار الحالي أقل من المطلوب، التحديث مطلوب
      if (versionNeedsUpdate) {
        print('الإصدار الحالي أقل من المطلوب - يتوفر تحديث جديد');
        return true;
      }

      // إذا كان الإصدار متساوي أو أحدث، فحص force_update
      if (versionNeedsUpdate == false) {
        if (forceUpdate) {
          print('الإصدار مطابق أو أحدث ولكن التحديث الإجباري مفعل');
          return true;
        } else {
          print('الإصدار مطابق أو أحدث والتحديث الإجباري غير مفعل - لا حاجة للتحديث');
          return false;
        }
      }

      print('لا يوجد تحديث مطلوب');
      return false;
    } catch (e) {
      print('خطأ في فحص التحديث: $e');
      return false;
    }
  }

  // دالة للتحقق من صحة تنسيق الإصدار
  bool _isValidVersionFormat(String version) {
    final RegExp versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    return versionRegex.hasMatch(version);
  }

  // دالة محسنة لمقارنة الإصدارات
  bool _compareVersions(String currentVersion, String latestVersion) {
    try {
      List<int> currentParts = currentVersion.split('.').map(int.parse).toList();
      List<int> latestParts = latestVersion.split('.').map(int.parse).toList();

      // التأكد من أن كلا الإصدارين لهما نفس عدد الأجزاء
      while (currentParts.length < latestParts.length) {
        currentParts.add(0);
      }
      while (latestParts.length < currentParts.length) {
        latestParts.add(0);
      }

      for (int i = 0; i < latestParts.length; i++) {
        if (latestParts[i] > currentParts[i]) {
          return true; // الإصدار الجديد أحدث
        }
        if (latestParts[i] < currentParts[i]) {
          return false; // الإصدار الحالي أحدث
        }
      }

      return false; // الإصدارات متساوية
    } catch (e) {
      print('خطأ في مقارنة الإصدارات: $e');
      return false;
    }
  }  Future<void> showForcedUpdateDialog(BuildContext context) async {
    final latestVersionData = await fetchLatestVersion();
    final telegramLink = latestVersionData?['telegram_link'] ?? 'https://t.me/your_telegram_channel';
    
    return showDialog(
      context: context,
      barrierDismissible: false, // منع الإغلاق بالنقر خارج النافذة
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // منع الإغلاق بزر الرجوع
          child: AlertDialog(
            title: Row(
              children: [
                Icon(Icons.system_update, color: Colors.red, size: 28),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'تحديث إجباري',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  size: 64,
                  color: Colors.orange,
                ),
                SizedBox(height: 16),
                Text(
                  'يتوفر تحديث مهم للتطبيق',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 12),
                Text(
                  'يجب تحديث التطبيق لضمان استمرار العمل بشكل صحيح وللحصول على أحدث الميزات والإصلاحات الأمنية.',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'لا يمكن تخطي هذا التحديث',
                          style: TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: <Widget>[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: Icon(Icons.download, color: Colors.white),
                  label: Text(
                    'تحميل التحديث الآن',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () async {
                    try {
                      print('محاولة فتح رابط التحديث: $telegramLink');
                      // Try to open Telegram channel for download
                      final intent = AndroidIntent(
                        action: 'android.intent.action.VIEW',
                        data: telegramLink,
                      );
                      await intent.launch();
                    } catch (e) {
                      print('خطأ في فتح رابط التليجرام: $e');
                      // Fallback to Play Store
                      try {
                        final intent = AndroidIntent(
                          action: 'android.intent.action.VIEW',
                          data: 'market://details?id=com.ispmanager.app',
                          package: 'com.android.vending',
                        );
                        await intent.launch();
                      } catch (e) {
                        print('خطأ في فتح البلاي ستور: $e');
                        // If Play Store app is not installed, open in browser
                        try {
                          final intent = AndroidIntent(
                            action: 'android.intent.action.VIEW',
                            data: 'https://play.google.com/store/apps/details?id=com.ispmanager.app',
                          );
                          await intent.launch();
                        } catch (e) {
                          print('خطأ في فتح المتصفح: $e');
                          // Show error message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('حدث خطأ في فتح رابط التحديث. يرجى تحديث التطبيق يدوياً.'),
                              backgroundColor: Colors.red,
                              duration: Duration(seconds: 5),
                            ),
                          );
                        }
                      }
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // دالة للتحقق الدوري من التحديثات (يمكن استخدامها في الخلفية)
  Future<void> checkForUpdatesInBackground(BuildContext context) async {
    try {
      final bool updateRequired = await isUpdateRequired();
      if (updateRequired) {
        // التأكد من أن النافذة لم تظهر مسبقاً
        if (Navigator.canPop(context)) {
          await showForcedUpdateDialog(context);
        }
      }
    } catch (e) {
      print('خطأ في الفحص الدوري للتحديثات: $e');
    }
  }

  // دالة لتحديث عمود force_update في قاعدة البيانات (للمطورين)
  Future<bool> setForceUpdate(String version, bool forceUpdate) async {
    try {
      await supabase
          .from('app_versions')
          .update({'force_update': forceUpdate})
          .eq('version', version);
      
      print('تم تحديث إعداد التحديث الإجباري للإصدار $version: $forceUpdate');
      return true;
    } catch (e) {
      print('خطأ في تحديث إعداد التحديث الإجباري: $e');
      return false;
    }
  }

  // دالة للحصول على معلومات مفصلة عن التحديث
  Future<Map<String, dynamic>> getUpdateInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final latestVersionData = await fetchLatestVersion();
    
    return {
      'currentVersion': packageInfo.version,
      'latestVersion': latestVersionData?['version'] ?? 'غير متوفر',
      'forceUpdate': latestVersionData?['force_update'] ?? false,
      'telegramLink': latestVersionData?['telegram_link'] ?? '',
      'updateRequired': await isUpdateRequired(),
    };
  }

  // دالة لتحليل حالة التحديث بالتفصيل (للمطورين والاختبار)
  Future<Map<String, dynamic>> analyzeUpdateStatus() async {
    try {
      final latestVersionData = await fetchLatestVersion();
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final String currentVersion = packageInfo.version;
      
      if (latestVersionData == null) {
        return {
          'status': 'error',
          'message': 'لا توجد بيانات في قاعدة البيانات',
          'current_version': currentVersion,
          'latest_version': null,
          'force_update': null,
          'update_required': false,
        };
      }

      final String latestVersion = latestVersionData['version'];
      final bool forceUpdate = latestVersionData['force_update'] ?? false;
      final bool versionNeedsUpdate = _compareVersions(currentVersion, latestVersion);
      final bool updateRequired = await isUpdateRequired();

      String analysisMessage;
      if (currentVersion == latestVersion) {
        if (forceUpdate) {
          analysisMessage = 'الإصدار مطابق ولكن التحديث إجباري';
        } else {
          analysisMessage = 'الإصدار مطابق ولا حاجة للتحديث';
        }
      } else if (versionNeedsUpdate) {
        analysisMessage = 'إصدار أحدث متوفر في قاعدة البيانات';
      } else {
        analysisMessage = 'الإصدار الحالي أحدث من قاعدة البيانات';
      }

      return {
        'status': 'success',
        'message': analysisMessage,
        'current_version': currentVersion,
        'latest_version': latestVersion,
        'force_update': forceUpdate,
        'update_required': updateRequired,
        'version_comparison': versionNeedsUpdate ? 'newer_available' : 'current_is_newer_or_equal',
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في تحليل حالة التحديث: $e',
        'current_version': null,
        'latest_version': null,
        'force_update': null,
        'update_required': false,
      };
    }
  }

  // دالة لطباعة تحليل مفصل (للتطوير والاختبار)
  Future<void> printDetailedAnalysis() async {
    print('========== تحليل مفصل لحالة التحديث ==========');
    final analysis = await analyzeUpdateStatus();
    
    analysis.forEach((key, value) {
      print('$key: $value');
    });
    
    print('===============================================');
  }
}
