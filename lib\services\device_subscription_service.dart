import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

import '../models/device_subscription_model.dart';

class DeviceSubscriptionService {
  final SupabaseClient _supabaseClient;
  final SharedPreferences _prefs;
  static const String _deviceIdKey = 'device_id';

  DeviceSubscriptionService(this._supabaseClient, this._prefs);

  /// Retrieves device information.
  Future<Map<String, String?>> _getDeviceInfo() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String? deviceName;
    String? deviceModel;
    String? deviceBrand;
    String? androidVersion;

    if (defaultTargetPlatform == TargetPlatform.android) {
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceName =
          androidInfo.model; // Android model is often used as device name
      deviceModel = androidInfo.model;
      deviceBrand = androidInfo.brand;
      androidVersion = androidInfo.version.release;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceName = iosInfo.name;
      deviceModel = iosInfo.model;
      deviceBrand = iosInfo.utsname.machine; // More specific hardware model
      androidVersion = iosInfo.systemVersion; // iOS version
    } else {
      // Handle other platforms if necessary, or return nulls
      deviceName = 'Unknown Device';
      deviceModel = 'Unknown Model';
      deviceBrand = 'Unknown Brand';
      androidVersion = 'Unknown OS Version';
    }

    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final String appVersion = packageInfo.version;

    return {
      'device_name': deviceName,
      'device_model': deviceModel,
      'device_brand': deviceBrand,
      'android_version': androidVersion,
      'app_version': appVersion,
    };
  }

  /// Generates a unique device ID and stores it locally.
  Future<String> _getOrCreateDeviceId() async {
    String? deviceId = _prefs.getString(_deviceIdKey);
    if (deviceId == null) {
      deviceId = const Uuid().v4();
      await _prefs.setString(_deviceIdKey, deviceId);
    }

    return deviceId;
  }

  /// Registers or fetches the device subscription.
  /// Returns the DeviceSubscription object if successful, null otherwise.
  Future<DeviceSubscription?> registerOrFetchDeviceSubscription(
    String? email,
  ) async {
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user!.uid)
        .get();
    final String deviceId = await _getOrCreateDeviceId();
    if (doc["role"] == "UserRole.admin") {
      try {
        // final String deviceId = await _getOrCreateDeviceId();
        final Map<String, String?> deviceInfo = await _getDeviceInfo();
        final String appVersion = deviceInfo['app_version'] ?? 'Unknown';

        // Try to fetch existing subscription

        final List<Map<String, dynamic>> response = await _supabaseClient
            .from('device_subscriptions')
            .select()
            .eq('user_email', email ?? "")
            .limit(1);

        if (response.isNotEmpty) {
          // Device already registered, update last_access and access_count
          final existingSubscription = DeviceSubscription.fromJson(
            response.first,
          );
          final int newAccessCount = existingSubscription.accessCount + 1;

          await _supabaseClient
              .from('device_subscriptions')
              .update({
                'last_access': DateTime.now().toIso8601String(),
                'access_count': newAccessCount,
                'app_version': appVersion,
                "user_email": email ?? "",
                // Always update app version
              })
              .eq('id', existingSubscription.id);

          // Re-fetch to get the updated data
          final List<Map<String, dynamic>> updatedResponse =
              await _supabaseClient
                  .from('device_subscriptions')
                  .select()
                  .eq('user_email', email ?? "")
                  .limit(1);

          if (updatedResponse.isNotEmpty) {
            return DeviceSubscription.fromJson(updatedResponse.first);
          }
          return existingSubscription; // Fallback if re-fetch fails
        } else {
          // New device, register it
          final response = await _supabaseClient.rpc('generate_account_number');
          final String newAccountNumber = response.toString();

          final Map<String, dynamic> newSubscriptionData = {
            'device_id': deviceId,
            'device_name': deviceInfo['device_name'],
            'device_model': deviceInfo['device_model'],
            'device_brand': deviceInfo['device_brand'],
            'android_version': deviceInfo['android_version'],
            'app_version': appVersion,
            'account_number': newAccountNumber,
            'subscription_start_date': DateTime.now().toIso8601String(),
            'subscription_end_date': DateTime.now()
                .add(const Duration(days: 30))
                .toIso8601String(), // Default 30 days
            'is_active': true,
            'last_access': DateTime.now().toIso8601String(),
            'access_count': 1,
            "user_email": email ?? "",
          };

          final List<Map<String, dynamic>> insertResponse =
              await _supabaseClient
                  .from('device_subscriptions')
                  .insert(newSubscriptionData)
                  .select();

          if (insertResponse.isNotEmpty) {
            return DeviceSubscription.fromJson(insertResponse.first);
          }
        }
      } catch (e) {
        debugPrint('Error in registerOrFetchDeviceSubscription: $e');
        return null;
      }
      return null;
    }
    return null;
  }

  /// Checks if the device subscription is active.
  Future<bool> isSubscriptionActive() async {
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user!.uid)
        .get();
    if (doc["role"] == "UserRole.admin") {
      try {
        final List<Map<String, dynamic>> response = await _supabaseClient
            .from('device_subscriptions')
            .select('subscription_end_date, is_active')
            .eq('user_email', user.email ?? "")
            .limit(1);

        if (response.isNotEmpty) {
          final DateTime endDate = DateTime.parse(
            response.first['subscription_end_date'],
          );
          final bool isActive = response.first['is_active'];
          return isActive && endDate.isAfter(DateTime.now());
        } else {
          return false;
        }
      } catch (e) {
        debugPrint('Error checking subscription status: $e');
      }
      return false;
      // Default to inactive if any error occurs or not found
    } else {
      return false;
    }
  }

  /// Fetches the current device subscription details.
  Future<DeviceSubscription?> getDeviceSubscription() async {
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user!.uid)
        .get();
    try {
      final data = doc.data();
      if (data!['role'] == "UserRole.admin") {
        final List<Map<String, dynamic>> response = await _supabaseClient
            .from('device_subscriptions')
            .select()
            .eq('user_email', user.email!)
            .limit(1);

        if (response.isNotEmpty) {
          return DeviceSubscription.fromJson(response.first);
        }
      }
    } catch (e) {
      print(e);
      debugPrint('Error fetching device subscription: $e');
    }
    return null;
  }

  /// Registers or updates device information (alias for registerOrFetchDeviceSubscription)
  Future<DeviceSubscription?> registerOrUpdateDevice() async {
    return await registerOrFetchDeviceSubscription("");
  }

  /// Checks if subscription is valid (not expired and active)
  Future<bool> isSubscriptionValid() async {
    final subscription = await getDeviceSubscription();
    if (subscription == null) return false;

    return subscription.isActive &&
        subscription.subscriptionEndDate.isAfter(DateTime.now());
  }

  /// Gets days remaining until subscription expires
  Future<int> getDaysRemaining() async {
    final subscription = await getDeviceSubscription();
    if (subscription == null) return 0;

    final now = DateTime.now();
    final endDate = subscription.subscriptionEndDate;

    if (endDate.isBefore(now)) return 0;

    return endDate.difference(now).inDays;
  }

  /// Requests subscription renewal (placeholder for future implementation)
  Future<bool> requestSubscriptionRenewal() async {
    // TODO: Implement renewal request logic
    // This could send a request to admin or payment system
    debugPrint('Subscription renewal requested');
    return false; // For now, return false (manual renewal required)
  }

  /// Fetches the current server time from Supabase
  Future<DateTime?> fetchServerTime() async {
    try {
      final response = await _supabaseClient.rpc('get_server_time');
      if (response != null && response is String) {
        return DateTime.parse(response);
      }
    } catch (e) {
      debugPrint('Error fetching server time: $e');
    }
    return null;
  }
}
