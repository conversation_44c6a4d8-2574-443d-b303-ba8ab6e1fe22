import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

import '../models/device_subscription_model.dart';

class DeviceSubscriptionService {
  final SupabaseClient _supabaseClient;
  final SharedPreferences _prefs;
  static const String _deviceIdKey = 'device_id';

  DeviceSubscriptionService(this._supabaseClient, this._prefs);

  /// Retrieves device information.
  Future<Map<String, String?>> _getDeviceInfo() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String? deviceName;
    String? deviceModel;
    String? deviceBrand;
    String? androidVersion;

    if (defaultTargetPlatform == TargetPlatform.android) {
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceName =
          androidInfo.model; // Android model is often used as device name
      deviceModel = androidInfo.model;
      deviceBrand = androidInfo.brand;
      androidVersion = androidInfo.version.release;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceName = iosInfo.name;
      deviceModel = iosInfo.model;
      deviceBrand = iosInfo.utsname.machine; // More specific hardware model
      androidVersion = iosInfo.systemVersion; // iOS version
    } else {
      // Handle other platforms if necessary, or return nulls
      deviceName = 'Unknown Device';
      deviceModel = 'Unknown Model';
      deviceBrand = 'Unknown Brand';
      androidVersion = 'Unknown OS Version';
    }

    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final String appVersion = packageInfo.version;

    return {
      'device_name': deviceName,
      'device_model': deviceModel,
      'device_brand': deviceBrand,
      'android_version': androidVersion,
      'app_version': appVersion,
    };
  }

  /// Generates a unique device ID and stores it locally.
  Future<String> _getOrCreateDeviceId() async {
    String? deviceId = _prefs.getString(_deviceIdKey);
    if (deviceId == null) {
      deviceId = const Uuid().v4();
      await _prefs.setString(_deviceIdKey, deviceId);
    }

    return deviceId;
  }

  /// Registers or fetches the device subscription.
  /// Returns the DeviceSubscription object if successful, null otherwise.
  Future<DeviceSubscription?> registerOrFetchDeviceSubscription(
    String? email,
  ) async {
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user!.uid)
        .get();
    final String deviceId = await _getOrCreateDeviceId();
    if (doc["role"] == "UserRole.admin") {
      try {
        // final String deviceId = await _getOrCreateDeviceId();
        final Map<String, String?> deviceInfo = await _getDeviceInfo();
        final String appVersion = deviceInfo['app_version'] ?? 'Unknown';

        // Try to fetch existing subscription
        // أولاً: البحث بالبريد الإلكتروني (النظام الجديد)
        List<Map<String, dynamic>> response = await _supabaseClient
            .from('device_subscriptions')
            .select()
            .eq('user_email', email ?? "")
            .limit(1);

        // ثانياً: إذا لم يتم العثور على اشتراك، ابحث بـ device_id (النظام القديم)
        if (response.isEmpty) {
          response = await _supabaseClient
              .from('device_subscriptions')
              .select()
              .eq('device_id', deviceId)
              .limit(1);

          // إذا وُجد اشتراك بـ device_id، قم بترحيله للنظام الجديد
          if (response.isNotEmpty) {
            print('🔄 تم العثور على اشتراك قديم في registerOrFetch، جاري الترحيل...');
            await _migrateOldSubscription(response.first, email ?? "");
          }
        }

        if (response.isNotEmpty) {
          // Device already registered, update last_access and access_count
          final existingSubscription = DeviceSubscription.fromJson(
            response.first,
          );
          final int newAccessCount = existingSubscription.accessCount + 1;

          await _supabaseClient
              .from('device_subscriptions')
              .update({
                'last_access': DateTime.now().toIso8601String(),
                'access_count': newAccessCount,
                'app_version': appVersion,
                "user_email": email ?? "",
                // Always update app version
              })
              .eq('id', existingSubscription.id);

          // Re-fetch to get the updated data
          final List<Map<String, dynamic>> updatedResponse =
              await _supabaseClient
                  .from('device_subscriptions')
                  .select()
                  .eq('user_email', email ?? "")
                  .limit(1);

          if (updatedResponse.isNotEmpty) {
            return DeviceSubscription.fromJson(updatedResponse.first);
          }
          return existingSubscription; // Fallback if re-fetch fails
        } else {
          // لم يتم العثور على اشتراك بالبريد أو بـ device_id
          // تحقق من وجود اشتراكات قديمة بنفس device_id بدون user_email أو user_email فارغ
          final List<Map<String, dynamic>> legacyResponse = await _supabaseClient
              .from('device_subscriptions')
              .select()
              .eq('device_id', deviceId)
              .or('user_email.is.null,user_email.eq.')
              .limit(1);

          if (legacyResponse.isNotEmpty) {
            // وُجد اشتراك قديم بدون user_email، قم بترحيله
            print('🔄 تم العثور على اشتراك قديم بدون user_email، جاري الترحيل التلقائي...');
            await _migrateOldSubscription(legacyResponse.first, email ?? "");

            // أعد جلب الاشتراك المُرحل
            final List<Map<String, dynamic>> migratedResponse = await _supabaseClient
                .from('device_subscriptions')
                .select()
                .eq('user_email', email ?? "")
                .limit(1);

            if (migratedResponse.isNotEmpty) {
              return DeviceSubscription.fromJson(migratedResponse.first);
            }
          }

          // New device, register it
          final response = await _supabaseClient.rpc('generate_account_number');
          final String newAccountNumber = response.toString();

          final Map<String, dynamic> newSubscriptionData = {
            'device_id': deviceId,
            'device_name': deviceInfo['device_name'],
            'device_model': deviceInfo['device_model'],
            'device_brand': deviceInfo['device_brand'],
            'android_version': deviceInfo['android_version'],
            'app_version': appVersion,
            'account_number': newAccountNumber,
            'subscription_start_date': DateTime.now().toIso8601String(),
            'subscription_end_date': DateTime.now()
                .add(const Duration(days: 30))
                .toIso8601String(), // Default 30 days
            'is_active': true,
            'last_access': DateTime.now().toIso8601String(),
            'access_count': 1,
            "user_email": email ?? "",
          };

          final List<Map<String, dynamic>> insertResponse =
              await _supabaseClient
                  .from('device_subscriptions')
                  .insert(newSubscriptionData)
                  .select();

          if (insertResponse.isNotEmpty) {
            return DeviceSubscription.fromJson(insertResponse.first);
          }
        }
      } catch (e) {
        debugPrint('Error in registerOrFetchDeviceSubscription: $e');
        return null;
      }
      return null;
    }
    return null;
  }

  /// Checks if the device subscription is active.
  Future<bool> isSubscriptionActive() async {
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user!.uid)
        .get();
    if (doc["role"] == "UserRole.admin") {
      try {
        // أولاً: البحث بالبريد الإلكتروني (النظام الجديد)
        List<Map<String, dynamic>> response = await _supabaseClient
            .from('device_subscriptions')
            .select('subscription_end_date, is_active')
            .eq('user_email', user.email ?? "")
            .limit(1);

        // ثانياً: إذا لم يتم العثور على اشتراك، ابحث بـ device_id (النظام القديم)
        if (response.isEmpty) {
          final String deviceId = await _getOrCreateDeviceId();
          response = await _supabaseClient
              .from('device_subscriptions')
              .select('subscription_end_date, is_active, user_email')
              .eq('device_id', deviceId)
              .limit(1);

          // إذا وُجد اشتراك بـ device_id، قم بترحيله للنظام الجديد
          if (response.isNotEmpty) {
            print('🔄 تم العثور على اشتراك قديم، جاري الترحيل...');
            await _migrateOldSubscription(response.first, user.email ?? "");
          }
        }

        if (response.isNotEmpty) {
          final DateTime endDate = DateTime.parse(
            response.first['subscription_end_date'],
          );
          final bool isActive = response.first['is_active'];
          return isActive && endDate.isAfter(DateTime.now());
        } else {
          return false;
        }
      } catch (e) {
        debugPrint('Error checking subscription status: $e');
      }
      return false;
      // Default to inactive if any error occurs or not found
    } else {
      return false;
    }
  }

  /// Fetches the current device subscription details.
  Future<DeviceSubscription?> getDeviceSubscription() async {
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user!.uid)
        .get();
    try {
      final data = doc.data();
      if (data!['role'] == "UserRole.admin") {
        // أولاً: البحث بالبريد الإلكتروني (النظام الجديد)
        List<Map<String, dynamic>> response = await _supabaseClient
            .from('device_subscriptions')
            .select()
            .eq('user_email', user.email!)
            .limit(1);

        // ثانياً: إذا لم يتم العثور على اشتراك، ابحث بـ device_id (النظام القديم)
        if (response.isEmpty) {
          final String deviceId = await _getOrCreateDeviceId();
          response = await _supabaseClient
              .from('device_subscriptions')
              .select()
              .eq('device_id', deviceId)
              .limit(1);

          // إذا وُجد اشتراك بـ device_id، قم بترحيله للنظام الجديد
          if (response.isNotEmpty) {
            print('🔄 تم العثور على اشتراك قديم في getDeviceSubscription، جاري الترحيل...');
            await _migrateOldSubscription(response.first, user.email!);
          }
        }

        if (response.isNotEmpty) {
          return DeviceSubscription.fromJson(response.first);
        }
      }
    } catch (e) {
      print(e);
      debugPrint('Error fetching device subscription: $e');
    }
    return null;
  }

  /// Registers or updates device information (alias for registerOrFetchDeviceSubscription)
  Future<DeviceSubscription?> registerOrUpdateDevice() async {
    return await registerOrFetchDeviceSubscription("");
  }

  /// Checks if subscription is valid (not expired and active)
  Future<bool> isSubscriptionValid() async {
    final subscription = await getDeviceSubscription();
    if (subscription == null) return false;

    return subscription.isActive &&
        subscription.subscriptionEndDate.isAfter(DateTime.now());
  }

  /// Gets days remaining until subscription expires
  Future<int> getDaysRemaining() async {
    final subscription = await getDeviceSubscription();
    if (subscription == null) return 0;

    final now = DateTime.now();
    final endDate = subscription.subscriptionEndDate;

    if (endDate.isBefore(now)) return 0;

    return endDate.difference(now).inDays;
  }

  /// Requests subscription renewal (placeholder for future implementation)
  Future<bool> requestSubscriptionRenewal() async {
    // TODO: Implement renewal request logic
    // This could send a request to admin or payment system
    debugPrint('Subscription renewal requested');
    return false; // For now, return false (manual renewal required)
  }

  /// Migrates old subscription from device_id to user_email system
  Future<void> _migrateOldSubscription(Map<String, dynamic> oldSubscription, String userEmail) async {
    try {
      print('🔄 بدء ترحيل الاشتراك القديم...');
      print('📱 Device ID: ${oldSubscription['device_id']}');
      print('📧 New Email: $userEmail');

      // تحقق من عدم وجود تضارب مع اشتراك آخر بنفس البريد
      final List<Map<String, dynamic>> existingEmailSub = await _supabaseClient
          .from('device_subscriptions')
          .select('id')
          .eq('user_email', userEmail)
          .neq('id', oldSubscription['id'])
          .limit(1);

      if (existingEmailSub.isNotEmpty) {
        print('⚠️ يوجد اشتراك آخر بنفس البريد، سيتم حذف الاشتراك المكرر...');
        // احذف الاشتراك المكرر واحتفظ بالأقدم
        await _supabaseClient
            .from('device_subscriptions')
            .delete()
            .eq('id', existingEmailSub.first['id']);
      }

      // تحديث الاشتراك القديم ليستخدم البريد الإلكتروني الجديد
      await _supabaseClient
          .from('device_subscriptions')
          .update({
            'user_email': userEmail,
            'migrated_at': DateTime.now().toIso8601String(),
            'last_access': DateTime.now().toIso8601String(),
          })
          .eq('id', oldSubscription['id']);

      print('✅ تم ترحيل الاشتراك بنجاح للبريد: $userEmail');
    } catch (e) {
      print('❌ خطأ في ترحيل الاشتراك: $e');
      // لا نرمي الخطأ لأن الاشتراك القديم لا يزال يعمل
    }
  }

  /// Advanced subscription check with automatic migration and conflict resolution
  Future<Map<String, dynamic>> performAdvancedSubscriptionCheck() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return {'success': false, 'error': 'المستخدم غير مسجل الدخول'};
    }

    try {
      print('🔍 بدء الفحص المتقدم للاشتراك...');

      final String deviceId = await _getOrCreateDeviceId();
      final String userEmail = user.email ?? "";

      // 1. البحث بالبريد الإلكتروني أولاً
      List<Map<String, dynamic>> emailSubs = await _supabaseClient
          .from('device_subscriptions')
          .select()
          .eq('user_email', userEmail)
          .order('created_at', ascending: false);

      // 2. البحث بـ device_id
      List<Map<String, dynamic>> deviceSubs = await _supabaseClient
          .from('device_subscriptions')
          .select()
          .eq('device_id', deviceId)
          .order('created_at', ascending: false);

      print('📧 اشتراكات بالبريد: ${emailSubs.length}');
      print('📱 اشتراكات بـ device_id: ${deviceSubs.length}');

      // 3. معالجة الحالات المختلفة
      if (emailSubs.isNotEmpty) {
        // يوجد اشتراك بالبريد - استخدمه
        print('✅ تم العثور على اشتراك بالبريد الإلكتروني');
        return {'success': true, 'subscription': emailSubs.first, 'method': 'email'};
      } else if (deviceSubs.isNotEmpty) {
        // يوجد اشتراك بـ device_id فقط - قم بترحيله
        print('🔄 تم العثور على اشتراك قديم، جاري الترحيل...');
        await _migrateOldSubscription(deviceSubs.first, userEmail);

        // أعد جلب الاشتراك المُرحل
        final migratedSubs = await _supabaseClient
            .from('device_subscriptions')
            .select()
            .eq('user_email', userEmail)
            .limit(1);

        if (migratedSubs.isNotEmpty) {
          print('✅ تم ترحيل الاشتراك بنجاح');
          return {'success': true, 'subscription': migratedSubs.first, 'method': 'migrated'};
        }
      }

      // 4. لا يوجد اشتراك - إنشاء جديد مطلوب
      print('❌ لم يتم العثور على اشتراك صالح');
      return {'success': false, 'error': 'لا يوجد اشتراك صالح'};

    } catch (e) {
      print('❌ خطأ في الفحص المتقدم: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Fetches the current server time from Supabase
  Future<DateTime?> fetchServerTime() async {
    try {
      final response = await _supabaseClient.rpc('get_server_time');
      if (response != null && response is String) {
        return DateTime.parse(response);
      }
    } catch (e) {
      debugPrint('Error fetching server time: $e');
    }
    return null;
  }
}
