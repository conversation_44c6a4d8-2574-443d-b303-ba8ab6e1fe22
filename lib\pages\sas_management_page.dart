import 'package:flutter/material.dart';
import '../services/sas_api_service.dart';
import '../models/sas_user_model.dart';
import '../services/database_service.dart';
import 'sync_progress_page.dart';
import 'test_sync_page.dart';

class SasManagementPage extends StatefulWidget {
  const SasManagementPage({super.key});

  @override
  State<SasManagementPage> createState() => _SasManagementPageState();
}

class _SasManagementPageState extends State<SasManagementPage> {
  final _formKey = GlobalKey<FormState>();
  final _hostController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _error;
  List<SasUser> _users = [];
  bool _loggedIn = false;
  String _syncProgress = ''; // Add progress tracking
  double _syncProgressValue = 0.0; // Add progress bar value

  @override
  void dispose() {
    _hostController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loginAndFetchUsers() async {
    print('==== [LOGIN] _loginAndFetchUsers CALLED ====');
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      final api = SasApiService();
      final success = await api.loginWithCredentials(
        host: _hostController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
      );
      if (!success) {
        setState(() {
          _error = 'فشل تسجيل الدخول. تحقق من البيانات.';
          _isLoading = false;
        });
        return;
      }
      List<SasUser> fetchedUsers = [];
      int currentPage = 1;
      int perPage = 500; // Increased back to 500 as requested
      int? totalUsers;
      int maxPages = 50; // Add safety limit
      bool shouldContinue = true;

      while (shouldContinue) {
        print('==== [LOGIN] Fetching users - Page $currentPage ====');
        final usersJson = await api.getUsers(page: currentPage, perPage: perPage);
        if (usersJson != null && usersJson['data'] is List) {
          final currentBatch = (usersJson['data'] as List)
              .map((u) {
                print('DEBUG: Raw user JSON: $u'); // Add this line
                return SasUser.fromJson(u as Map<String, dynamic>);
              })
              .toList();
          
          print('==== [LOGIN] Fetched ${currentBatch.length} users from page $currentPage ====');
          
          if (currentBatch.isNotEmpty) {
            fetchedUsers.addAll(currentBatch);
          }

          // Attempt to get total users from the response, if available
          if (usersJson.containsKey('total') && usersJson['total'] is int) {
            totalUsers = usersJson['total'];
            print('==== [LOGIN] Total users reported by API: $totalUsers ====');
          } else if (usersJson.containsKey('meta') && usersJson['meta']['total'] is int) {
            // Common pattern for pagination metadata
            totalUsers = usersJson['meta']['total'];
            print('==== [LOGIN] Total users from meta: $totalUsers ====');
          }

          // Stop conditions
          if (currentBatch.isEmpty && currentPage > 1) {
            print('==== [LOGIN] Empty page detected, stopping ====');
            shouldContinue = false;
          } else if (totalUsers != null && fetchedUsers.length >= totalUsers) {
            print('==== [LOGIN] Reached reported total users: $totalUsers ====');
            shouldContinue = false;
          } else if (currentPage >= maxPages) {
            print('==== [LOGIN] Reached maximum page limit ($maxPages), stopping ====');
            shouldContinue = false;
          } else {
            currentPage++;
          }
        } else {
          print('==== [LOGIN] Invalid response format, stopping ====');
          shouldContinue = false;
        }
      }

      _users = fetchedUsers;
      print('==== [LOGIN] Found ${fetchedUsers.length} users from SAS ====');
      setState(() {
        _loggedIn = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'خطأ: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _syncSasUsersToSubscribers() async {
    print('==== [SYNC] _syncSasUsersToSubscribers called ====');
    print('==== [SYNC] Host: ${_hostController.text.trim()} ====');
    print('==== [SYNC] Username: ${_usernameController.text.trim()} ====');
    
    try {
      // الانتقال إلى شاشة المزامنة التفاعلية
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SyncProgressPage(
            host: _hostController.text.trim(),
            username: _usernameController.text.trim(),
            password: _passwordController.text,
          ),
        ),
      );
      print('==== [SYNC] Navigation completed successfully ====');
    } catch (e) {
      print('==== [SYNC] Navigation failed: $e ====');
      // إظهار رسالة خطأ للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح شاشة المزامنة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SAS Radius 4 Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const TestSyncPage(),
                ),
              );
            },
            tooltip: 'اختبار شاشة المزامنة',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingScreen()
          : _loggedIn
              ? _buildUsersList()
              : _buildLoginForm(),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (_syncProgress.isNotEmpty) ...[
            Text(
              _syncProgress,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
          ],
          CircularProgressIndicator(
            value: _syncProgressValue > 0 ? _syncProgressValue : null,
          ),
          SizedBox(height: 20),
          if (_syncProgressValue > 0)
            Text(
              '${(_syncProgressValue * 100).toInt()}%',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
        ],
      ),
    );
  }

  Widget _buildLoginForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextFormField(
              controller: _hostController,
              decoration: const InputDecoration(
                labelText: 'SAS Host (مثال: demo4.sasradius.com)',
                border: OutlineInputBorder(),
              ),
              validator: (v) => v == null || v.isEmpty ? 'أدخل عنوان السيرفر' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _usernameController,
              decoration: const InputDecoration(
                labelText: 'اسم المستخدم',
                border: OutlineInputBorder(),
              ),
              validator: (v) => v == null || v.isEmpty ? 'أدخل اسم المستخدم' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              validator: (v) => v == null || v.isEmpty ? 'أدخل كلمة المرور' : null,
            ),
            const SizedBox(height: 24),
            if (_error != null)
              Text(_error!, style: const TextStyle(color: Colors.red)),
            ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  _loginAndFetchUsers();
                }
              },
              child: const Text('تسجيل الدخول وجلب المستخدمين'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'قائمة المستخدمين',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'عدد المستخدمين: ${_users.length}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.sync),
                    label: const Text('مزامنة مع المشتركين'),
                    onPressed: _isLoading ? null : () {
                      print('==== [SYNC] Sync button pressed ====');
                      _syncSasUsersToSubscribers();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.logout),
                    onPressed: () {
                      // فقط تسجيل الخروج عند رغبة المستخدم، لا تعيد تعيين _loggedIn و _users عند مغادرة الصفحة
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('تأكيد تسجيل الخروج'),
                          content: const Text('هل أنت متأكد أنك تريد تسجيل الخروج من SAS؟'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('إلغاء'),
                            ),
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _loggedIn = false;
                                  _users = [];
                                });
                                Navigator.of(context).pop();
                              },
                              child: const Text('تسجيل الخروج'),
                            ),
                          ],
                        ),
                      );
                    },
                    tooltip: 'تسجيل الخروج',
                  ),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: _users.isEmpty
              ? const Center(child: Text('لا يوجد مستخدمين'))
              : ListView.builder(
                  itemCount: _users.length,
                  itemBuilder: (context, index) {
                    final user = _users[index];
                    return ListTile(
                      leading: const Icon(Icons.person),
                      title: Text(user.fullName),
                      subtitle: Text(user.username),
                      trailing: user.isActive
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : const Icon(Icons.cancel, color: Colors.red),
                    );
                  },
                ),
        ),
      ],
    );
  }
}
