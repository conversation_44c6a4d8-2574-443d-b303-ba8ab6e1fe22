import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../models/device_subscription_model.dart';
import 'device_subscription_service.dart';

class SubscriptionCheckService {
  final SupabaseClient _supabase;
  final SharedPreferences _prefs;
  late final DeviceSubscriptionService _deviceService;

  SubscriptionCheckService(this._supabase, this._prefs) {
    _deviceService = DeviceSubscriptionService(_supabase, _prefs);
  }

  // فحص شامل للاشتراك عند تشغيل التطبيق
  Future<Map<String, dynamic>> checkSubscriptionStatus() async {
    try {
      print('========== فحص حالة الاشتراك ==========');
        // تسجيل/تحديث الجهاز
      final subscription = await _deviceService.registerOrUpdateDevice();
      print('معرف الجهاز: ${subscription?.deviceId ?? 'غير متوفر'}');
      print('رقم الحساب: ${subscription?.accountNumber ?? 'غير متوفر'}');
      print('تاريخ الانتهاء: ${subscription?.subscriptionEndDate ?? 'غير متوفر'}');
      print('الحالة: ${subscription?.isActive == true ? "نشط" : "غير نشط"}');
      // فحص صلاحية الاشتراك
      final isValid = await _deviceService.isSubscriptionValid();
      final daysRemaining = await _deviceService.getDaysRemaining();
      
      print('صلاحية الاشتراك: ${isValid ? "صالح" : "منتهي"}');
      print('الأيام المتبقية: $daysRemaining');
      print('========================================');

      return {
        'subscription': subscription,
        'isValid': isValid,
        'daysRemaining': daysRemaining,
        'needsRenewal': !isValid || daysRemaining <= 3,
      };
    } catch (e) {
      print('خطأ في فحص الاشتراك: $e');
      return {
        'subscription': null,
        'isValid': false,
        'daysRemaining': 0,
        'needsRenewal': true,
        'error': e.toString(),
      };
    }
  }

  // عرض نافذة انتهاء الاشتراك (مثل التحديث الإجباري)
  Future<void> showSubscriptionExpiredDialog(BuildContext context, DeviceSubscription subscription) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Row(
              children: [
                Icon(Icons.block, color: Colors.red, size: 28),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'انتهى الاشتراك',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.schedule,
                  size: 64,
                  color: Colors.orange,
                ),
                SizedBox(height: 16),
                Text(
                  'انتهت صلاحية اشتراكك',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 12),
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_circle, color: Colors.blue, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'رقم الحساب: ${subscription.accountNumber}',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.calendar_today, color: Colors.red, size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'انتهى في: ${subscription.subscriptionEndDate.day}/${subscription.subscriptionEndDate.month}/${subscription.subscriptionEndDate.year}',
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  'يرجى التواصل مع المطور لتجديد الاشتراك',
                  style: TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: <Widget>[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: Icon(Icons.contact_support, color: Colors.white),
                  label: Text(
                    'طلب تجديد الاشتراك',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () async {                    // TODO: Add reason for renewal to logs: 'طلب تجديد من التطبيق' + 'الجهاز: ${subscription.deviceName ?? "غير محدد"}'
                    await _deviceService.requestSubscriptionRenewal();
                    
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم إرسال طلب التجديد بنجاح'),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // عرض تحذير قرب انتهاء الاشتراك
  Future<void> showSubscriptionWarningDialog(BuildContext context, DeviceSubscription subscription, int daysRemaining) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange, size: 28),
              SizedBox(width: 8),
              Text('تحذير انتهاء الاشتراك'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.access_time,
                size: 48,
                color: Colors.orange,
              ),
              SizedBox(height: 16),
              Text(
                'سينتهي اشتراكك خلال $daysRemaining أيام',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12),
              Text(
                'رقم حسابك: ${subscription.accountNumber}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('تذكيرني لاحقاً'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();                // TODO: Add reason for renewal to logs: 'طلب تجديد مبكر' + 'تبقى $daysRemaining أيام'
                await _deviceService.requestSubscriptionRenewal();
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم إرسال طلب التجديد'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: Text('طلب التجديد'),
            ),
          ],
        );
      },
    );
  }
}
