import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = Uuid();

  // Collection references
  CollectionReference get _sasServersCollection => _firestore.collection('sas_servers');
  CollectionReference get _mikrotikDevicesCollection => _firestore.collection('mikrotik_devices');
  CollectionReference get _usersCollection => _firestore.collection('users');
  CollectionReference get _packagesCollection => _firestore.collection('packages');
  CollectionReference get _subscribersCollection => _firestore.collection('subscribers');
  CollectionReference get _activityLogsCollection => _firestore.collection('activity_logs');
  CollectionReference get _paymentRecordsCollection => _firestore.collection('payment_records');
  CollectionReference get _messageTemplatesCollection => _firestore.collection('message_templates');
  CollectionReference get _expenseCategoriesCollection => _firestore.collection('expense_categories');
  CollectionReference get _expensesCollection => _firestore.collection('expenses');

  // Initialize Firebase
  Future<void> initialize() async {
    // Firebase is automatically initialized when the app starts
    print('==== [Firebase] Firebase initialized ====');
  }

  // Utility method to convert data for Firestore
  Map<String, dynamic> prepareForFirestore(Map<String, dynamic> data) {
    final newData = <String, dynamic>{};
    data.forEach((key, value) {
      if (value is DateTime) {
        newData[key] = Timestamp.fromDate(value);
      } else if (value is bool) {
        newData[key] = value;
      } else {
        newData[key] = value;
      }
    });
    newData.removeWhere((key, value) => value == null);
    return newData;
  }

  // Utility method to convert Firestore data back
  Map<String, dynamic> prepareFromFirestore(Map<String, dynamic> data) {
    final newData = <String, dynamic>{};
    data.forEach((key, value) {
      if (value is Timestamp) {
        newData[key] = value.toDate();
      } else {
        newData[key] = value;
      }
    });
    return newData;
  }

  // SasServer operations
  Future<List<Map<String, dynamic>>> getSasServers() async {
    try {
      final snapshot = await _sasServersCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting SAS servers: $e');
      return [];
    }
  }

  Future<String> insertSasServer(Map<String, dynamic> server) async {
    try {
      final docRef = await _sasServersCollection.add(prepareForFirestore(server));
      return docRef.id;
    } catch (e) {
      print('Error inserting SAS server: $e');
      rethrow;
    }
  }

  Future<void> updateSasServer(Map<String, dynamic> server) async {
    try {
      await _sasServersCollection.doc(server['id']).update(prepareForFirestore(server));
    } catch (e) {
      print('Error updating SAS server: $e');
      rethrow;
    }
  }

  Future<void> deleteSasServer(String id) async {
    try {
      await _sasServersCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting SAS server: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getSasServerById(String id) async {
    try {
      final doc = await _sasServersCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting SAS server by ID: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getConnectedSasServer() async {
    try {
      final snapshot = await _sasServersCollection
          .where('isConnected', isEqualTo: true)
          .limit(1)
          .get();
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;
        data['id'] = snapshot.docs.first.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting connected SAS server: $e');
      return null;
    }
  }

  // MikrotikDevice operations
  Future<List<Map<String, dynamic>>> getMikrotikDevices() async {
    try {
      final snapshot = await _mikrotikDevicesCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting Mikrotik devices: $e');
      return [];
    }
  }

  Future<String> insertMikrotikDevice(Map<String, dynamic> device) async {
    try {
      final docRef = await _mikrotikDevicesCollection.add(prepareForFirestore(device));
      return docRef.id;
    } catch (e) {
      print('Error inserting Mikrotik device: $e');
      rethrow;
    }
  }

  Future<void> updateMikrotikDevice(Map<String, dynamic> device) async {
    try {
      await _mikrotikDevicesCollection.doc(device['id']).update(prepareForFirestore(device));
    } catch (e) {
      print('Error updating Mikrotik device: $e');
      rethrow;
    }
  }

  Future<void> deleteMikrotikDevice(String id) async {
    try {
      await _mikrotikDevicesCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting Mikrotik device: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getMikrotikDeviceById(String id) async {
    try {
      final doc = await _mikrotikDevicesCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting Mikrotik device by ID: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getConnectedMikrotikDevice() async {
    try {
      final snapshot = await _mikrotikDevicesCollection
          .where('isConnected', isEqualTo: true)
          .limit(1)
          .get();
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;
        data['id'] = snapshot.docs.first.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting connected Mikrotik device: $e');
      return null;
    }
  }

  // User operations
  Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      final snapshot = await _usersCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting users: $e');
      return [];
    }
  }

  Future<String> insertUser(Map<String, dynamic> user) async {
    try {
      final docRef = await _usersCollection.add(prepareForFirestore(user));
      return docRef.id;
    } catch (e) {
      print('Error inserting user: $e');
      rethrow;
    }
  }

  Future<void> updateUser(Map<String, dynamic> user) async {
    try {
      await _usersCollection.doc(user['id']).update(prepareForFirestore(user));
    } catch (e) {
      print('Error updating user: $e');
      rethrow;
    }
  }

  Future<void> deleteUser(String id) async {
    try {
      await _usersCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting user: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getUserById(String id) async {
    try {
      final doc = await _usersCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getUserByUsername(String username) async {
    try {
      final snapshot = await _usersCollection
          .where('username', isEqualTo: username)
          .limit(1)
          .get();
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;
        data['id'] = snapshot.docs.first.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting user by username: $e');
      return null;
    }
  }

  // Package operations
  Future<List<Map<String, dynamic>>> getPackages() async {
    try {
      final snapshot = await _packagesCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting packages: $e');
      return [];
    }
  }

  Future<String> insertPackage(Map<String, dynamic> package) async {
    try {
      final docRef = await _packagesCollection.add(prepareForFirestore(package));
      return docRef.id;
    } catch (e) {
      print('Error inserting package: $e');
      rethrow;
    }
  }

  Future<void> updatePackage(Map<String, dynamic> package) async {
    try {
      await _packagesCollection.doc(package['id']).update(prepareForFirestore(package));
    } catch (e) {
      print('Error updating package: $e');
      rethrow;
    }
  }

  Future<void> deletePackage(String id) async {
    try {
      await _packagesCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting package: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getPackageById(String id) async {
    try {
      final doc = await _packagesCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting package by ID: $e');
      return null;
    }
  }

  // Subscriber operations
  Future<List<Map<String, dynamic>>> getSubscribers() async {
    try {
      final snapshot = await _subscribersCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting subscribers: $e');
      return [];
    }
  }

  Future<String> insertSubscriber(Map<String, dynamic> subscriber) async {
    try {
      final docRef = await _subscribersCollection.add(prepareForFirestore(subscriber));
      return docRef.id;
    } catch (e) {
      print('Error inserting subscriber: $e');
      rethrow;
    }
  }

  Future<void> updateSubscriber(Map<String, dynamic> subscriber) async {
    try {
      await _subscribersCollection.doc(subscriber['id']).update(prepareForFirestore(subscriber));
    } catch (e) {
      print('Error updating subscriber: $e');
      rethrow;
    }
  }

  Future<void> deleteSubscriber(String id) async {
    try {
      await _subscribersCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting subscriber: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getSubscriberById(String id) async {
    try {
      final doc = await _subscribersCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  // Activity log operations
  Future<List<Map<String, dynamic>>> getActivityLogs() async {
    try {
      final snapshot = await _activityLogsCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting activity logs: $e');
      return [];
    }
  }

  Future<String> insertActivityLog(Map<String, dynamic> log) async {
    try {
      final docRef = await _activityLogsCollection.add(prepareForFirestore(log));
      return docRef.id;
    } catch (e) {
      print('Error inserting activity log: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getActivityLogsBySubscriber(String subscriberId) async {
    try {
      final snapshot = await _activityLogsCollection
          .where('subscriberId', isEqualTo: subscriberId)
          .get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting activity logs by subscriber: $e');
      return [];
    }
  }

  // Payment record operations
  Future<List<Map<String, dynamic>>> getPaymentRecords() async {
    try {
      final snapshot = await _paymentRecordsCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting payment records: $e');
      return [];
    }
  }

  Future<String> insertPaymentRecord(Map<String, dynamic> record) async {
    try {
      final docRef = await _paymentRecordsCollection.add(prepareForFirestore(record));
      return docRef.id;
    } catch (e) {
      print('Error inserting payment record: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getPaymentRecordsBySubscriber(String subscriberId) async {
    try {
      final snapshot = await _paymentRecordsCollection
          .where('subscriberId', isEqualTo: subscriberId)
          .get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting payment records by subscriber: $e');
      return [];
    }
  }

  // Message Template operations
  Future<List<Map<String, dynamic>>> getMessageTemplates() async {
    try {
      final snapshot = await _messageTemplatesCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting message templates: $e');
      return [];
    }
  }

  Future<String> insertMessageTemplate(Map<String, dynamic> template) async {
    try {
      final docRef = await _messageTemplatesCollection.add(prepareForFirestore(template));
      return docRef.id;
    } catch (e) {
      print('Error inserting message template: $e');
      rethrow;
    }
  }

  Future<void> updateMessageTemplate(Map<String, dynamic> template) async {
    try {
      await _messageTemplatesCollection.doc(template['id']).update(prepareForFirestore(template));
    } catch (e) {
      print('Error updating message template: $e');
      rethrow;
    }
  }

  Future<void> deleteMessageTemplate(String id) async {
    try {
      await _messageTemplatesCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting message template: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getMessageTemplateById(String id) async {
    try {
      final doc = await _messageTemplatesCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting message template by ID: $e');
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> getMessageTemplatesByType(int type) async {
    try {
      final snapshot = await _messageTemplatesCollection
          .where('type', isEqualTo: type)
          .get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting message templates by type: $e');
      return [];
    }
  }

  // Expense Category operations
  Future<List<Map<String, dynamic>>> getExpenseCategories() async {
    try {
      final snapshot = await _expenseCategoriesCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting expense categories: $e');
      return [];
    }
  }

  Future<String> insertExpenseCategory(Map<String, dynamic> category) async {
    try {
      final docRef = await _expenseCategoriesCollection.add(prepareForFirestore(category));
      return docRef.id;
    } catch (e) {
      print('Error inserting expense category: $e');
      rethrow;
    }
  }

  Future<void> updateExpenseCategory(Map<String, dynamic> category) async {
    try {
      await _expenseCategoriesCollection.doc(category['id']).update(prepareForFirestore(category));
    } catch (e) {
      print('Error updating expense category: $e');
      rethrow;
    }
  }

  Future<void> deleteExpenseCategory(String id) async {
    try {
      await _expenseCategoriesCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting expense category: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getExpenseCategoryById(String id) async {
    try {
      final doc = await _expenseCategoriesCollection.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }
      return null;
    } catch (e) {
      print('Error getting expense category by ID: $e');
      return null;
    }
  }

  // Expense operations
  Future<List<Map<String, dynamic>>> getExpenses() async {
    try {
      final snapshot = await _expensesCollection.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting expenses: $e');
      return [];
    }
  }

  Future<String> insertExpense(Map<String, dynamic> expense) async {
    try {
      final docRef = await _expensesCollection.add(prepareForFirestore(expense));
      return docRef.id;
    } catch (e) {
      print('Error inserting expense: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getExpensesByCategoryId(String categoryId) async {
    try {
      final snapshot = await _expensesCollection
          .where('categoryId', isEqualTo: categoryId)
          .get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return prepareFromFirestore(data);
      }).toList();
    } catch (e) {
      print('Error getting expenses by category: $e');
      return [];
    }
  }

  Future<void> deleteExpense(String expenseId) async {
    try {
      await _expensesCollection.doc(expenseId).delete();
    } catch (e) {
      print('Error deleting expense: $e');
      rethrow;
    }
  }

  // Database utilities
  Future<void> clearAllTables() async {
    try {
      // Delete all documents from all collections
      await _deleteCollection(_expensesCollection);
      await _deleteCollection(_expenseCategoriesCollection);
      await _deleteCollection(_paymentRecordsCollection);
      await _deleteCollection(_activityLogsCollection);
      await _deleteCollection(_subscribersCollection);
      await _deleteCollection(_packagesCollection);
      await _deleteCollection(_messageTemplatesCollection);
      await _deleteCollection(_usersCollection);
      await _deleteCollection(_sasServersCollection);
      await _deleteCollection(_mikrotikDevicesCollection);
    } catch (e) {
      print('Error clearing all tables: $e');
      rethrow;
    }
  }

  Future<void> _deleteCollection(CollectionReference collection) async {
    final snapshot = await collection.get();
    final batch = _firestore.batch();
    for (final doc in snapshot.docs) {
      batch.delete(doc.reference);
    }
    await batch.commit();
  }

  // Migration from SQLite to Firebase
  Future<void> migrateFromSQLite(List<Map<String, dynamic>> data, String collectionName) async {
    try {
      final collection = _firestore.collection(collectionName);
      final batch = _firestore.batch();
      
      for (final item in data) {
        final docRef = collection.doc();
        batch.set(docRef, prepareForFirestore(item));
      }
      
      await batch.commit();
      print('==== [Firebase] Migrated ${data.length} items to $collectionName ====');
    } catch (e) {
      print('Error migrating to Firebase: $e');
      rethrow;
    }
  }

  // Generate unique ID
  String generateId() => _uuid.v4();
} 