import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/vpn_settings_model.dart';
import '../services/vpn_service.dart';

class VpnSettingsPage extends StatefulWidget {
  const VpnSettingsPage({super.key});

  @override
  State<VpnSettingsPage> createState() => _VpnSettingsPageState();
}

class _VpnSettingsPageState extends State<VpnSettingsPage> {
  VpnSettings? _settings;
  bool _isLoading = true;
  bool _isConnecting = false;
  bool _isTesting = false;
  final _networkIdController = TextEditingController();
  final _networkNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
 final user = FirebaseAuth.instance.currentUser;
  @override
  void initState() {
    super.initState();
    _loadSettings();
    _listenToSettingsChanges();
  }

  @override
  void dispose() {
    _networkIdController.dispose();
    _networkNameController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await VpnService.getSettings();
      if (mounted) {
        setState(() {
          _settings = settings;
          _networkIdController.text = settings.networkId ?? '';
          _networkNameController.text = settings.networkName ?? '';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading VPN settings: $e');
      if (mounted) {
        setState(() {
          _settings = VpnSettings.defaultSettings();
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل إعدادات VPN: ${e.toString()}'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _listenToSettingsChanges() {
    VpnService.settingsStream.listen(
      (settings) {
        if (mounted) {
          setState(() {
            _settings = settings;
            _isConnecting = settings.isConnecting;
          });
        }
      },
      onError: (error) {
        debugPrint('Error listening to VPN settings: $error');
      },
    );
  }

  Future<void> _toggleVpn() async {
    if (_settings == null) return;

    setState(() {
      _isConnecting = true;
    });

    try {
      if (_settings!.isConnected) {
        final success = await VpnService.stopVpn();
        if (!success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إيقاف VPN'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        final success = await VpnService.startVpn();
        if (!success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في تشغيل VPN'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
      }
    }
  }

  Future<void> _saveNetworkSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await VpnService.updateNetwork(
        networkId: _networkIdController.text.trim(),
        networkName: _networkNameController.text.trim(),
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ إعدادات الشبكة بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في حفظ إعدادات الشبكة'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testNetwork() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isTesting = true;
    });

    try {
      final isValid = await VpnService.testNetworkConnectivity(
        _networkIdController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isValid 
                ? 'الشبكة صالحة ويمكن الاتصال بها' 
                : 'فشل في الاتصال بالشبكة - تأكد من صحة المعرف وإضافة الجهاز للشبكة'
            ),
            backgroundColor: isValid ? Colors.green : Colors.red,
            duration: Duration(seconds: isValid ? 3 : 5),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختبار الشبكة: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isTesting = false;
        });
      }
    }
  }

  Future<void> _toggleAutoConnect() async {
    if (_settings == null) return;

    try {
      final newSettings = _settings!.copyWith(
        autoConnect: !_settings!.autoConnect, userId: user!.uid,
      );
      await VpnService.saveSettings(newSettings);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newSettings.autoConnect 
                ? 'تم تفعيل الاتصال التلقائي' 
                : 'تم إلغاء الاتصال التلقائي'
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإعدادات: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('إعدادات VPN'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات VPN'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Connection Status Card
            _buildStatusCard(),
            const SizedBox(height: 20),

            // Network Configuration Card
            _buildNetworkConfigCard(),
            const SizedBox(height: 20),

            // Connection Control Card
            _buildConnectionControlCard(),
            const SizedBox(height: 20),

            // Information Card
            _buildInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.vpn_key,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'حالة الاتصال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  _settings?.statusIcon ?? '🔌',
                  style: const TextStyle(fontSize: 32),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _settings?.statusText ?? 'غير متصل',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (_settings?.lastConnected != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'آخر اتصال: ${DateFormat('yyyy/MM/dd HH:mm').format(_settings!.lastConnected!)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkConfigCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.network_check,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'إعدادات الشبكة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _networkIdController,
                decoration: const InputDecoration(
                  labelText: 'معرف الشبكة (Network ID)',
                  hintText: 'مثال: 8056c2e21c000001',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.fingerprint),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال معرف الشبكة';
                  }
                  if (!VpnService.isValidNetworkId(value.trim())) {
                    return 'معرف الشبكة غير صحيح (يجب أن يكون 16 حرف)';
                  }
                  return null;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9a-fA-F]')),
                  LengthLimitingTextInputFormatter(16),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _networkNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الشبكة (اختياري)',
                  hintText: 'مثال: شبكة المكتب الرئيسي',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.label),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isTesting ? null : _testNetwork,
                      icon: _isTesting 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.wifi_find),
                      label: Text(_isTesting ? 'جاري الاختبار...' : 'اختبار الشبكة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _saveNetworkSettings,
                      icon: _isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                      label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ الإعدادات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConnectionControlCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.power_settings_new,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'التحكم في الاتصال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _settings?.hasNetwork == true && !_isConnecting 
                        ? _toggleVpn 
                        : null,
                    icon: _isConnecting 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Icon(_settings?.isConnected == true 
                            ? Icons.stop 
                            : Icons.play_arrow),
                    label: Text(_isConnecting 
                        ? 'جاري الاتصال...' 
                        : (_settings?.isConnected == true ? 'إيقاف VPN' : 'تشغيل VPN')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _settings?.isConnected == true 
                          ? Colors.red 
                          : Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('الاتصال التلقائي'),
              subtitle: const Text('اتصال تلقائي عند تشغيل التطبيق'),
              value: _settings?.autoConnect ?? false,
              onChanged: (value) => _toggleAutoConnect(),
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_settings?.nodeIdentity != null) ...[
              _buildInfoRow('معرف العقدة:', _settings!.nodeIdentity!),
              const SizedBox(height: 8),
            ],
            _buildInfoRow('معرف الشبكة:', _settings?.networkId ?? 'غير محدد'),
            if (_settings?.networkName != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow('اسم الشبكة:', _settings!.networkName!),
            ],
            if (_settings?.assignedIp != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow('العنوان المخصص:', _settings!.assignedIp!),
            ],
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ ZeroTier مفعل',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'النظام يعمل مع ZeroTier الحقيقي للاتصال الآمن بالأجهزة البعيدة.',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💡 ملاحظة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '• تأكد من إضافة هذا الجهاز إلى شبكة ZeroTier الخاصة بك\n'
                    '• يجب أن يكون معرف الشبكة صحيحاً ومكون من 16 حرف\n'
                    '• الاتصال قد يستغرق بضع ثوانٍ\n'
                    '• قد تحتاج إلى موافقة من مدير الشبكة',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
        ),
      ],
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة VPN'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'كيفية إعداد VPN للوصول للأجهزة البعيدة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('1. إنشاء شبكة ZeroTier على my.zerotier.com'),
              Text('2. الحصول على معرف الشبكة (Network ID)'),
              Text('3. إضافة هذا الجهاز إلى الشبكة'),
              Text('4. إدخال معرف الشبكة في التطبيق'),
              Text('5. تشغيل VPN للاتصال'),
              SizedBox(height: 12),
              Text(
                'مميزات VPN:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• الوصول الآمن للأجهزة البعيدة'),
              Text('• تشفير البيانات'),
              Text('• تجاوز قيود الشبكة'),
              Text('• إدارة مركزية للأجهزة'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
} 