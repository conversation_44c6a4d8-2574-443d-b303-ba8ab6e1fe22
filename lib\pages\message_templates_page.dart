import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/message_template_model.dart';
import '../services/database_service.dart';
import '../services/app_settings_service.dart';

class MessageTemplatesPage extends StatefulWidget {
  const MessageTemplatesPage({super.key});

  @override
  State<MessageTemplatesPage> createState() => _MessageTemplatesPageState();
}

class _MessageTemplatesPageState extends State<MessageTemplatesPage> {
  List<MessageTemplateModel> _templates = [];
  bool _isLoading = true;
  MessageTemplateType? _filterType;
  final DatabaseService _databaseService = DatabaseService();

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    setState(() => _isLoading = true);
    try {
      await DatabaseService().syncTemplatesToFirebase();
      var templates = await _databaseService.getMessageTemplatesFire();
      if (templates.isEmpty) {
        templates = await DatabaseService().getMessageTemplates();
      }
      setState(() {
        _templates = templates;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorDialog('خطأ في تحميل القوالب: $e');
    }
  }

  Future<void> _showEditDialog({MessageTemplateModel? template}) async {
    final isEdit = template != null;
    final nameController = TextEditingController(text: template?.name ?? '');
    final contentController = TextEditingController(
      text: template?.content ?? '',
    );
    MessageTemplateType selectedType =
        template?.type ?? MessageTemplateType.renewal;
    bool isDefault = template?.isDefault ?? false;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                isEdit ? Icons.edit : Icons.add,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(isEdit ? 'تعديل القالب' : 'إضافة قالب جديد'),
            ],
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.8,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم القالب',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.label),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 0.0,
                    ), // Adjust padding as needed
                    child: DropdownButtonFormField<MessageTemplateType>(
                      value: selectedType,
                      items: MessageTemplateType.values.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (type) {
                        if (type != null) {
                          setDialogState(() => selectedType = type);
                        }
                      },
                      decoration: const InputDecoration(
                        labelText: 'نوع القالب',
                        border: OutlineInputBorder(),
                        // Removed prefixIcon to prevent overflow
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: contentController,
                    maxLines: 8,
                    decoration: InputDecoration(
                      labelText: 'نص الرسالة',
                      border: const OutlineInputBorder(),
                      alignLabelWithHint: true,
                      suffixIcon: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.info_outline),
                            tooltip: 'المتغيرات المتاحة',
                            onPressed: () => _showVariablesDialog(),
                          ),
                          IconButton(
                            icon: const Icon(Icons.preview),
                            tooltip: 'معاينة القالب',
                            onPressed: () async =>
                                await _previewTemplate(contentController.text),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Checkbox(
                        value: isDefault,
                        onChanged: (val) {
                          setDialogState(() => isDefault = val ?? false);
                        },
                      ),
                      const Expanded(
                        child: Text('اجعل هذا القالب افتراضي لهذا النوع'),
                      ),
                    ],
                  ),
                  if (isDefault)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Colors.orange.shade700,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'سيتم استبدال القالب الافتراضي الحالي لهذا النوع',
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty ||
                    contentController.text.trim().isEmpty) {
                  _showErrorDialog('يرجى ملء جميع الحقول المطلوبة');
                  return;
                }

                try {
                  final newTemplate = MessageTemplateModel(
                    adminId: DatabaseService().adminId,
                    id: template?.id ?? _databaseService.generateId(),
                    name: nameController.text.trim(),
                    content: contentController.text.trim(),
                    type: selectedType,
                    createdAt: template?.createdAt ?? DateTime.now(),
                    isDefault: isDefault,
                  );

                  if (isEdit) {
                    await _databaseService.updateMessageTemplate(newTemplate);
                  } else {
                    await _databaseService.addMessageTemplate(newTemplate);
                  }

                  Navigator.pop(context, true);
                } catch (e) {
                  _showErrorDialog('خطأ في حفظ القالب: $e');
                }
              },
              child: Text(isEdit ? 'حفظ التعديلات' : 'إضافة'),
            ),
          ],
        ),
      ),
    );

    if (result == true) {
      await _loadTemplates();
    }
  }

  Future<void> _showVariablesDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: const [
            Icon(Icons.code, color: Colors.blue),
            SizedBox(width: 8),
            Text('المتغيرات المتاحة'),
          ],
        ),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: SingleChildScrollView(
            child: DataTable(
              columns: const [
                DataColumn(
                  label: Text(
                    'المتغير',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'الوصف',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(label: Text('نسخ')),
              ],
              rows: MessageTemplateModel.availableVariables.entries.map((
                entry,
              ) {
                return DataRow(
                  cells: [
                    DataCell(
                      SelectableText(
                        entry.key,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    DataCell(
                      Text(entry.value, style: const TextStyle(fontSize: 14)),
                    ),
                    DataCell(
                      IconButton(
                        icon: const Icon(Icons.copy, size: 20),
                        tooltip: 'نسخ المتغير',
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: entry.key));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('تم نسخ ${entry.key}'),
                              duration: const Duration(seconds: 1),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _previewTemplate(String content) async {
    // تحميل تنسيق العملة من الإعدادات
    final packagePrice = await AppSettingsService.formatCurrency(100);
    final debtAmount = await AppSettingsService.formatCurrency(150);
    final paymentAmount = await AppSettingsService.formatCurrency(100);

    // قيم تجريبية للمعاينة
    final previewContent = content
        .replaceAll('{subscriber_name}', 'أحمد محمد')
        .replaceAll('{package_name}', 'باقة 50 ميجا')
        .replaceAll('{package_price}', packagePrice)
        .replaceAll('{package_duration}', '30 يوم')
        .replaceAll('{package_speed}', '50 ميجا')
        .replaceAll('{package_devices}', '5')
        .replaceAll('{start_date}', '2024-01-01')
        .replaceAll('{end_date}', '2024-01-31')
        .replaceAll('{days_remaining}', '15')
        .replaceAll('{debt_amount}', debtAmount)
        .replaceAll('{payment_amount}', paymentAmount)
        .replaceAll('{payment_date}', '2024-01-15');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.preview, color: Colors.green),
            SizedBox(width: 8),
            Text('معاينة الرسالة'),
          ],
        ),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'هذه معاينة للرسالة بالقيم التجريبية:',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Text(
                  previewContent,
                  style: const TextStyle(fontSize: 16, height: 1.5),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTemplate(MessageTemplateModel template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.delete_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Text('هل أنت متأكد من حذف القالب "${template.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _databaseService.deleteMessageTemplate(template.id);
        await _loadTemplates();
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف القالب بنجاح')));
        }
      } catch (e) {
        _showErrorDialog('خطأ في حذف القالب: $e');
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  List<MessageTemplateModel> get _filteredTemplates {
    if (_filterType == null) return _templates;
    return _templates.where((t) => t.type == _filterType).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة قوالب الرسائل'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTemplates,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط معلومات العملة
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade700, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'ملاحظة: المتغيرات المالية ({package_price}, {debt_amount}, {payment_amount}) تستخدم العملة المحددة في إعدادات التطبيق',
                    style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                  ),
                ),
              ],
            ),
          ),
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.filter_list, color: Colors.grey),
                const SizedBox(width: 8),
                Expanded(
                  child: Row(
                    children: [
                      const Text(
                        'تصفية حسب النوع:',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<MessageTemplateType?>(
                          value: _filterType,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 8,
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          items: [
                            const DropdownMenuItem<MessageTemplateType?>(
                              value: null,
                              child: Text(
                                'جميع الأنواع',
                                style: TextStyle(fontSize: 13.0),
                              ),
                            ),
                            ...MessageTemplateType.values.map(
                              (type) => DropdownMenuItem(
                                value: type,
                                child: Text(
                                  type.displayName,
                                  style: TextStyle(fontSize: 13.0),
                                ),
                              ),
                            ),
                          ],
                          onChanged: (type) {
                            setState(() => _filterType = type);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // محتوى الصفحة
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTemplates.isEmpty
                ? _buildEmptyState()
                : _buildTemplatesList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showEditDialog(),
        icon: const Icon(Icons.add),
        label: const Text('إضافة قالب'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.message_outlined, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            _filterType == null
                ? 'لا توجد قوالب رسائل'
                : 'لا توجد قوالب لهذا النوع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإضافة قالب جديد للبدء',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showEditDialog(),
            icon: const Icon(Icons.add),
            label: const Text('إضافة قالب جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesList() {
    return RefreshIndicator(
      onRefresh: _loadTemplates,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredTemplates.length,
        itemBuilder: (context, index) {
          final template = _filteredTemplates[index];
          return _buildTemplateCard(template);
        },
      ),
    );
  }

  Widget _buildTemplateCard(MessageTemplateModel template) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              template.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (template.isDefault)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade100,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.orange.shade300,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 14,
                                    color: Colors.orange.shade700,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'افتراضي',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.orange.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getTypeColor(template.type).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _getTypeColor(
                              template.type,
                            ).withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          template.type.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getTypeColor(template.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showEditDialog(template: template);
                        break;
                      case 'preview':
                        _previewTemplate(template.content);
                        break;
                      case 'delete':
                        _deleteTemplate(template);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'preview',
                      child: Row(
                        children: [
                          Icon(Icons.preview, size: 18),
                          SizedBox(width: 8),
                          Text('معاينة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // محتوى الرسالة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                template.content.length > 150
                    ? '${template.content.substring(0, 150)}...'
                    : template.content,
                style: const TextStyle(fontSize: 14, height: 1.4),
              ),
            ),

            const SizedBox(height: 12),

            // تذييل البطاقة
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'تم الإنشاء: ${_formatDate(template.createdAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _showEditDialog(template: template),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('تعديل'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(MessageTemplateType type) {
    switch (type) {
      case MessageTemplateType.renewal:
        return Colors.blue;
      case MessageTemplateType.payment:
        return Colors.green;
      case MessageTemplateType.reminder:
        return Colors.orange;
      case MessageTemplateType.welcome:
        return Colors.purple;
      case MessageTemplateType.expiry:
        return Colors.red;
      case MessageTemplateType.custom:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
