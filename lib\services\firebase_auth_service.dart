import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:isp_manager/services/database_service.dart';
import '../models/user_model.dart';

class FirebaseAuthService {
  static final FirebaseAuthService _instance = FirebaseAuthService._internal();
  factory FirebaseAuthService() => _instance;
  FirebaseAuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current Firebase user
  User? get currentUser => _auth.currentUser;

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Check if user is authenticated and session is valid
  Future<bool> isUserAuthenticated() async {
    try {
      final user = currentUser;
      if (user == null) {
        print('❌ لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // تحديث معلومات المستخدم للتأكد من صحة الجلسة
      await user.reload();
      final refreshedUser = _auth.currentUser;

      if (refreshedUser == null) {
        print('❌ انتهت صلاحية جلسة المستخدم');
        return false;
      }

      print('✅ المستخدم مصادق عليه: ${refreshedUser.email}');
      return true;
    } catch (e) {
      print('❌ خطأ في التحقق من المصادقة: $e');
      return false;
    }
  }

  // Sign up with email and password
  Future<UserCredential> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
    required String phoneNumber,
    required UserRole role,
  }) async {
    try {
      // Create user with Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Create user document in Firestore
      final user = UserModel(
        adminId: userCredential.user!.uid,
        id: userCredential.user!.uid,
        username: email,
        password: '', // Don't store password in Firestore
        phoneNumber: phoneNumber,
        role: role,
        fullName: fullName,
        createdAt: DateTime.now(),
        isActive: true,
        permissions: Permission.allPermissions, // هنا بنضيف كل الصلاحيات
      );

      await _firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .set(user.toMap());

      // Update Firebase user display name
      await userCredential.user!.updateDisplayName(fullName);

      return userCredential;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _auth.signOut();
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Get user data from Firestore
  Future<UserModel?> getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data()!);
      }

      // If user document doesn't exist, create a default one
      print('==== [AUTH] User document not found, creating default user ====');
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        final defaultUser = UserModel(
          adminId: doc['adminId'] ?? uid,
          id: uid,
          username: currentUser.email ?? '',
          password: '',
          phoneNumber: currentUser.phoneNumber ?? '',
          role: UserRole.admin, // Default role
          fullName:
              currentUser.displayName ??
              currentUser.email?.split('@')[0] ??
              'User',
          createdAt: DateTime.now(),
          isActive: true,
          permissions: Permission.allPermissions,
        );

        await _firestore.collection('users').doc(uid).set(defaultUser.toMap());

        return defaultUser;
      }

      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  // Update user profile
  Future<void> updateUserProfile({
    required String uid,
    required String fullName,
    required String phoneNumber,
    UserRole? role,
  }) async {
    try {
      final updates = <String, dynamic>{
        'fullName': fullName,
        'phoneNumber': phoneNumber,
      };

      if (role != null) {
        updates['role'] = role.toString();
      }

      await _firestore.collection('users').doc(uid).update(updates);

      // Update Firebase user display name
      if (currentUser != null) {
        await currentUser!.updateDisplayName(fullName);
      }
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user logged in');

      // Re-authenticate user before changing password
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      await user.reauthenticateWithCredential(credential);
      await user.updatePassword(newPassword);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Delete user account
  Future<void> deleteUserAccount(String password) async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user logged in');

      // Re-authenticate user before deleting account
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );

      await user.reauthenticateWithCredential(credential);

      // Delete user document from Firestore
      await _firestore.collection('users').doc(user.uid).delete();

      // Delete Firebase user
      await user.delete();
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Check if email is already registered
  Future<bool> isEmailRegistered(String email) async {
    try {
      final methods = await _auth.fetchSignInMethodsForEmail(email);
      return methods.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Handle Firebase Auth errors and convert to user-friendly messages
  String _handleAuthError(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'لم يتم العثور على المستخدم بهذا البريد الإلكتروني';
        case 'wrong-password':
          return 'كلمة المرور غير صحيحة';
        case 'email-already-in-use':
          return 'البريد الإلكتروني مستخدم بالفعل';
        case 'weak-password':
          return 'كلمة المرور ضعيفة جداً. يجب أن تكون 6 أحرف على الأقل';
        case 'invalid-email':
          return 'البريد الإلكتروني غير صحيح';
        case 'user-disabled':
          return 'تم تعطيل هذا الحساب';
        case 'too-many-requests':
          return 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً';
        case 'operation-not-allowed':
          return 'تسجيل الحساب معطل حالياً';
        case 'network-request-failed':
          return 'فشل الاتصال بالشبكة. يرجى التحقق من اتصال الإنترنت';
        default:
          return 'حدث خطأ غير متوقع: ${error.message}';
      }
    }
    return 'حدث خطأ غير متوقع';
  }

  // Verify email
  Future<void> sendEmailVerification() async {
    try {
      final user = currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Check if email is verified
  bool get isEmailVerified => currentUser?.emailVerified ?? false;

  // Reload user to get latest data
  Future<void> reloadUser() async {
    await currentUser?.reload();
  }

  // Get all users (admin only)
  Future<List<UserModel>> getAllUsers() async {
    final adminId = DatabaseService().adminId;
    try {
      final querySnapshot = await _firestore
          .collection('users')
          .where('adminId', isEqualTo: adminId)
          .get();
      return querySnapshot.docs
          .map((doc) => UserModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting all users: $e');
      throw Exception('فشل في جلب المستخدمين: $e');
    }
  }

  // Create new user (admin only)
  Future<void> createUser({
    required String fullName,
    required String username,
    required String password,
    required String phoneNumber,
    required UserRole role,
    required bool isActive,
    required String adminId,
    required List<String> permissions,
  }) async {
    try {
      // Check if username/email already exists
      final isRegistered = await isEmailRegistered(username);
      if (isRegistered) {
        throw Exception('البريد الإلكتروني مستخدم بالفعل');
      }

      // Create user with Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: username,
        password: password,
      );

      // Create user document in Firestore
      final user = UserModel(
        adminId: (adminId.isNotEmpty)
            ? adminId
            : FirebaseAuth.instance.currentUser!.uid,
        id: userCredential.user!.uid,
        username: username,
        password: '', // Don't store password in Firestore
        phoneNumber: phoneNumber,
        role: role,
        fullName: fullName,
        createdAt: DateTime.now(),
        isActive: isActive,
        permissions: permissions,
      );

      await _firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .set(user.toMap());

      // Update Firebase user display name
      await userCredential.user!.updateDisplayName(fullName);

      // If user is not active, disable the account
      if (!isActive) {
        await userCredential.user!.updateDisplayName('$fullName (معطل)');
      }
    } catch (e) {
      print(e);
      // throw _handleAuthError(e);
    }
  }

  // Update user (admin only)
  Future<void> updateUser({
    required String uid,
    required String fullName,
    required String username,
    required String phoneNumber,
    required UserRole role,
    required bool isActive,
    required List<String> permissions,
  }) async {
    try {
      // Update user document in Firestore
      final updates = <String, dynamic>{
        'fullName': fullName,
        'username': username,
        'phoneNumber': phoneNumber,
        'role': role.toString(),
        'isActive': isActive,
        'permissions': permissions,
      };

      await _firestore.collection('users').doc(uid).update(updates);

      // لا يمكن تحديث displayName لمستخدم آخر من تطبيق العميل
      // فقط حدث بيانات Firestore
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Delete user (admin only)
  Future<void> deleteUser(String uid) async {
    try {
      // Delete user document from Firestore
      await _firestore.collection('users').doc(uid).delete();

      // Delete Firebase user (requires admin privileges)
      // Note: This might require additional setup in Firebase Admin SDK
      // For now, we'll just delete the Firestore document
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Get user by UID
  Future<UserModel?> getUserById(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }

  // Check if current user has permission
  Future<bool> hasPermission(String permission) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      final userData = await getUserData(currentUser.uid);
      if (userData == null) return false;

      return userData.hasPermission(permission);
    } catch (e) {
      print('Error checking permission: $e');
      return false;
    }
  }
}
