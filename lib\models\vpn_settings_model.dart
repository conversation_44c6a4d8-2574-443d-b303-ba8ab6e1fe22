// VPN Settings Model for ZeroTier configuration
class VpnSettings {
  final bool isEnabled;
  final String? networkId;
  final String? networkName;
  final String? nodeIdentity;
  final String? assignedIp;
  final bool autoConnect;
  final List<String> allowedDevices;
  final DateTime? lastConnected;
  final String
  connectionStatus; // 'connected', 'connecting', 'disconnected', 'error'
  final String userId;
  VpnSettings({
    this.isEnabled = false,
    this.networkId,
    this.networkName,
    this.nodeIdentity,
    this.assignedIp,
    this.autoConnect = false,
    List<String>? allowedDevices,
    this.lastConnected,
    required this.userId,
    this.connectionStatus = 'disconnected',
  }) : allowedDevices = allowedDevices ?? [];

  // Default settings
  factory VpnSettings.defaultSettings() {
    return VpnSettings(
      userId: "",
      isEnabled: false,
      autoConnect: false,
      connectionStatus: 'disconnected',
    );
  }

  // Convert to Map for SharedPreferences
  Map<String, dynamic> toMap() {
    return {
      'isEnabled': isEnabled,
      'networkId': networkId,
      'networkName': networkName,
      'nodeIdentity': nodeIdentity,
      'assignedIp': assignedIp,
      'autoConnect': autoConnect,
      'allowedDevices': allowedDevices,
      'lastConnected': lastConnected?.toIso8601String(),
      'connectionStatus': connectionStatus,
      "userId": userId,
    };
  }

  // Create from Map
  factory VpnSettings.fromMap(Map<String, dynamic> map) {
    return VpnSettings(
      isEnabled: map['isEnabled'] ?? false,
      networkId: map['networkId'],
      networkName: map['networkName'],
      nodeIdentity: map['nodeIdentity'],
      assignedIp: map['assignedIp'],
      autoConnect: map['autoConnect'] ?? false,
      allowedDevices: (map['allowedDevices'] as List?)?.cast<String>() ?? [],
      lastConnected: map['lastConnected'] != null
          ? DateTime.tryParse(map['lastConnected'])
          : null,
      connectionStatus: map['connectionStatus'] ?? 'disconnected',
      userId: map["userId"] ?? "",
    );
  }

  // Copy with method for updates
  VpnSettings copyWith({
    required String userId,
    bool? isEnabled,
    String? networkId,
    String? networkName,
    String? nodeIdentity,
    String? assignedIp,
    bool? autoConnect,
    List<String>? allowedDevices,
    DateTime? lastConnected,
    String? connectionStatus,
  }) {
    return VpnSettings(
      isEnabled: isEnabled ?? this.isEnabled,
      networkId: networkId ?? this.networkId,
      networkName: networkName ?? this.networkName,
      nodeIdentity: nodeIdentity ?? this.nodeIdentity,
      assignedIp: assignedIp ?? this.assignedIp,
      autoConnect: autoConnect ?? this.autoConnect,
      allowedDevices: allowedDevices ?? this.allowedDevices,
      lastConnected: lastConnected ?? this.lastConnected,
      connectionStatus: connectionStatus ?? this.connectionStatus,
      userId: userId,
    );
  }

  // Helper methods
  bool get isConnected => connectionStatus == 'connected';
  bool get isConnecting => connectionStatus == 'connecting';
  bool get hasNetwork => networkId != null && networkId!.isNotEmpty;

  String get statusText {
    switch (connectionStatus) {
      case 'connected':
        return 'متصل';
      case 'connecting':
        return 'جاري الاتصال...';
      case 'error':
        return 'خطأ في الاتصال';
      default:
        return 'غير متصل';
    }
  }

  String get statusIcon {
    switch (connectionStatus) {
      case 'connected':
        return '🔗';
      case 'connecting':
        return '⏳';
      case 'error':
        return '❌';
      default:
        return '🔌';
    }
  }
}
