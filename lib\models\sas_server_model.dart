import 'dart:convert';

class SasServerModel {
  String adminId;
  final String? id;
  final String name;
  final String host;
  final String username;
  final String password;
  final bool isConnected;

  SasServerModel({
    this.id,
    required this.name,
    required this.adminId,
    required this.host,
    required this.username,
    required this.password,
    this.isConnected = false,
  });

  SasServerModel copyWith({
    String? id,
    String? name,
    String? host,
    String? username,
    String? password,
    bool? isConnected,
    required String adminId,
  }) {
    return SasServerModel(
      adminId: adminId,
      id: id ?? this.id,
      name: name ?? this.name,
      host: host ?? this.host,
      username: username ?? this.username,
      password: password ?? this.password,
      isConnected: isConnected ?? this.isConnected,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'host': host,
      'username': username,
      'password': password,
      'isConnected': isConnected ? 1 : 0,
      "adminId": adminId,
    };
  }

  factory SasServerModel.fromMap(Map<String, dynamic> map) {
    return SasServerModel(
      adminId: map['adminId'] ?? "",
      id: map['id'].toString(),
      name: map['name'] as String,
      host: map['host'] as String,
      username: map['username'] as String,
      password: map['password'] as String,
      isConnected: map['isConnected'] == 1,
    );
  }

  String toJson() => json.encode(toMap());

  factory SasServerModel.fromJson(String source) =>
      SasServerModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'SasServerModel(id: $id, name: $name, host: $host, username: $username, password: $password, isConnected: $isConnected)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SasServerModel &&
        other.id == id &&
        other.name == name &&
        other.host == host &&
        other.username == username &&
        other.password == password &&
        other.isConnected == isConnected;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        host.hashCode ^
        username.hashCode ^
        password.hashCode ^
        isConnected.hashCode;
  }
}
