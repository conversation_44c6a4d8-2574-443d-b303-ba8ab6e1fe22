class MikrotikDevice {
  String? id;
  String name;
  String? host;
  int port;
  String username;
  String password;
  bool isConnected;

  MikrotikDevice({
    this.id,
    required this.name,
    required this.host,
    this.port = 8728, // Default API port
    required this.username,
    required this.password,
    this.isConnected = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'host': host,
      'port': port,
      'username': username,
      'password': password,
      'isConnected': isConnected ? 1 : 0,
    };
  }

  static MikrotikDevice fromMap(Map<String, dynamic> map) {
    return MikrotikDevice(
      id: map['id'],
      name: map['name'],
      host: map['host'],
      port: map['port'],
      username: map['username'],
      password: map['password'],
      isConnected: map['isConnected'] == 1,
    );
  }
}

class MikrotikDeviceModel {
  final String? id;
  final String name;
  final String host;
  final int port;
  final String username;
  final String password;
  bool isConnected;
  String adminId;
  MikrotikDeviceModel({
    this.id,
    required this.name,
    required this.host,
    this.port = 8728,
    required this.username,
    required this.password,
    required this.adminId,
    this.isConnected = false,
  });

  Map<String, dynamic> toMap() => {
    'id': id,
    'name': name,
    'host': host,
    'port': port,
    'username': username,
    'password': password,
    'isConnected': isConnected,
    'adminId': isConnected,
  };

  factory MikrotikDeviceModel.fromMap(Map<String, dynamic> map) =>
      MikrotikDeviceModel(
        id: map['id']?.toString(),
        name: map['name'] ?? "",
        host: map['host'] ?? "",
        port: map['port'] ?? "",
        username: map['username'] ?? "",
        password: map['password'] ?? "",
        isConnected: map['isConnected'] == 1 || map['isConnected'] == true,
        adminId: map['adminId'] ?? "",
      );

  MikrotikDeviceModel copyWith({
    String? id,
    String? name,
    String? host,
    int? port,
    String? username,
    String? password,
    bool? isConnected,
    required String adminId,
  }) => MikrotikDeviceModel(
    id: id ?? this.id,
    name: name ?? this.name,
    host: host ?? this.host,
    port: port ?? this.port,
    username: username ?? this.username,
    password: password ?? this.password,
    isConnected: isConnected ?? this.isConnected,
    adminId: adminId,
  );
}
