import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:path/path.dart' as path_lib;
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class SQLiteService {
  static final SQLiteService _instance = SQLiteService._internal();
  factory SQLiteService() => _instance;
  SQLiteService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = path_lib.join(documentsDirectory.path, 'isp_manager.db');
    return await openDatabase(
      path,
      version: 8, // Increment version for sellingPrice column
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  // Force database recreation for development/testing
  Future<void> recreateDatabase() async {
    await _database?.close();
    _database = null;
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = path_lib.join(documentsDirectory.path, 'isp_manager.db');
    if (await File(path).exists()) {
      await File(path).delete();
    }
    print('==== [DB] Database file deleted, will recreate on next access ====');
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create sas_servers table
    await db.execute('''
      CREATE TABLE sas_servers(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        host TEXT NOT NULL,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        isConnected INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create mikrotik_devices table
    await db.execute('''
      CREATE TABLE mikrotik_devices(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        host TEXT NOT NULL,
        port INTEGER NOT NULL,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        isConnected INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id TEXT PRIMARY KEY,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        phoneNumber TEXT NOT NULL,
        role TEXT NOT NULL,
        fullName TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        isActive INTEGER NOT NULL
      )
    ''');

    // Create packages table
    await db.execute('''
      CREATE TABLE packages(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        durationInDays INTEGER NOT NULL,
        speed TEXT NOT NULL,
        deviceCount INTEGER NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        isActive INTEGER NOT NULL,
        sasProfileId TEXT,
        sellingPrice REAL
      )
    ''');

    // Create subscribers table
    await db.execute('''
      CREATE TABLE subscribers(
        id TEXT PRIMARY KEY,
        fullName TEXT NOT NULL,
        phoneNumber TEXT NOT NULL,
        packageId TEXT NOT NULL,
        packageName TEXT NOT NULL DEFAULT '', -- New column for package name
        address TEXT NOT NULL,
        paymentStatus TEXT NOT NULL,
        subscriptionStart TEXT NOT NULL,
        subscriptionEnd TEXT NOT NULL,
        macAddress TEXT,
        routerName TEXT,
        technicalNotes TEXT,
        debtAmount REAL NOT NULL,
        createdAt TEXT NOT NULL,
        isActive INTEGER NOT NULL,
        subscriptionType TEXT,
        username TEXT,
        password TEXT,
        sasServerId INTEGER,
        FOREIGN KEY (packageId) REFERENCES packages (id)
      )
    ''');

    // Create activity logs table
    await db.execute('''
      CREATE TABLE activity_logs(
        id TEXT PRIMARY KEY,
        subscriberId TEXT NOT NULL,
        userId TEXT NOT NULL,
        action TEXT NOT NULL,
        description TEXT NOT NULL,
        amount REAL NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (subscriberId) REFERENCES subscribers (id),
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Create payment records table
    await db.execute('''
      CREATE TABLE payment_records(
        id TEXT PRIMARY KEY,
        subscriberId TEXT NOT NULL,
        amount REAL NOT NULL,
        paymentMethod TEXT NOT NULL,
        notes TEXT,
        paymentDate TEXT NOT NULL,
        recordedBy TEXT NOT NULL,
        FOREIGN KEY (subscriberId) REFERENCES subscribers (id),
        FOREIGN KEY (recordedBy) REFERENCES users (id)
      )
    ''');

    // Create message templates table
    await db.execute('''
      CREATE TABLE message_templates(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        content TEXT NOT NULL,
        type INTEGER NOT NULL,
        createdAt TEXT NOT NULL,
        isDefault INTEGER NOT NULL
      )
    ''');

    // Create expense categories table
    await db.execute('''
      CREATE TABLE expense_categories(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        createdAt TEXT NOT NULL
      )
    ''');

    // Create expenses table
    await db.execute('''
      CREATE TABLE expenses(
        id TEXT PRIMARY KEY,
        categoryId TEXT NOT NULL,
        amount REAL NOT NULL,
        notes TEXT,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (categoryId) REFERENCES expense_categories (id)
      )
    ''');
  }

  Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    print(
      '==== [DB] Database upgrade from version $oldVersion to $newVersion ====',
    );
    if (oldVersion < 2) {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN subscriptionType TEXT',
      );
      await db.execute('ALTER TABLE subscribers ADD COLUMN username TEXT');
      await db.execute('ALTER TABLE subscribers ADD COLUMN password TEXT');
    }
    if (oldVersion < 3) {
      // Create expense categories table
      await db.execute('''
        CREATE TABLE expense_categories(
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          createdAt TEXT NOT NULL
        )
      ''');

      // Create expenses table
      await db.execute('''
        CREATE TABLE expenses(
          id TEXT PRIMARY KEY,
          categoryId TEXT NOT NULL,
          amount REAL NOT NULL,
          notes TEXT,
          timestamp TEXT NOT NULL,
          FOREIGN KEY (categoryId) REFERENCES expense_categories (id)
        )
      ''');
    }
    if (oldVersion < 4) {
      // Add packageName column to subscribers table
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN packageName TEXT NOT NULL DEFAULT \'\'',
      );
    }
    if (oldVersion < 5) {
      // Create sas_servers table
      await db.execute('''
        CREATE TABLE sas_servers(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          host TEXT NOT NULL,
          username TEXT NOT NULL,
          password TEXT NOT NULL,
          isConnected INTEGER NOT NULL DEFAULT 0
        )
      ''');
    }
    if (oldVersion < 6) {
      // Add SAS integration columns
      print('==== [DB] Adding SAS integration columns ====');
      await db.execute('ALTER TABLE packages ADD COLUMN sasProfileId TEXT');
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN sasServerId INTEGER',
      );
      print('==== [DB] SAS integration columns added successfully ====');
    }
    if (oldVersion < 7) {
      // Create mikrotik_devices table
      print('==== [DB] Adding mikrotik_devices table ====');
      await db.execute('''
        CREATE TABLE mikrotik_devices(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          host TEXT NOT NULL,
          port INTEGER NOT NULL,
          username TEXT NOT NULL,
          password TEXT NOT NULL,
          isConnected INTEGER NOT NULL DEFAULT 0
        )
      ''');
      print('==== [DB] mikrotik_devices table added successfully ====');
    }
    if (oldVersion < 8) {
      // Add sellingPrice column to packages table
      print('==== [DB] Adding sellingPrice column to packages table ====');
      await db.execute('ALTER TABLE packages ADD COLUMN sellingPrice REAL');
      print('==== [DB] sellingPrice column added successfully ====');
    }
  }

  // SasServer operations
  Future<List<Map<String, dynamic>>> getSasServers() async {
    final db = await database;
    return await db.query('sas_servers');
  }

  Future<int> insertSasServer(Map<String, dynamic> server) async {
    final db = await database;
    return await db.insert('sas_servers', prepareForDb(server));
  }

  Future<int> updateSasServer(Map<String, dynamic> server) async {
    final db = await database;
    return await db.update(
      'sas_servers',
      prepareForDb(server),
      where: 'id = ?',
      whereArgs: [server['id']],
    );
  }

  Future<int> deleteSasServer(int id) async {
    final db = await database;
    return await db.delete('sas_servers', where: 'id = ?', whereArgs: [id]);
  }

  Future<Map<String, dynamic>?> getSasServerById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('sas_servers')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getConnectedSasServer() async {
    final db = await database;
    List<Map<String, dynamic>> results = await db.query(
      'sas_servers',
      where: 'isConnected = ?',
      whereArgs: [1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  // MikrotikDevice operations
  Future<List<Map<String, dynamic>>> getMikrotikDevices() async {
    final db = await database;
    return await db.query('mikrotik_devices');
  }

  Future<int> insertMikrotikDevice(Map<String, dynamic> device) async {
    final db = await database;
    return await db.insert('mikrotik_devices', prepareForDb(device));
  }

  Future<int> updateMikrotikDevice(Map<String, dynamic> device) async {
    final db = await database;
    return await db.update(
      'mikrotik_devices',
      prepareForDb(device),
      where: 'id = ?',
      whereArgs: [device['id']],
    );
  }

  Future<int> deleteMikrotikDevice(int id) async {
    final db = await database;
    return await db.delete(
      'mikrotik_devices',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<Map<String, dynamic>?> getMikrotikDeviceById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('mikrotik_devices')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getConnectedMikrotikDevice() async {
    final db = await database;
    List<Map<String, dynamic>> results = await db.query(
      'mikrotik_devices',
      where: 'isConnected = ?',
      whereArgs: [1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  // User operations
  Future<List<Map<String, dynamic>>> getUsers() async {
    final db = await database;
    return await db.query('users');
  }

  Future<int> insertUser(Map<String, dynamic> user) async {
    final db = await database;
    return await db.insert('users', prepareForDb(user));
  }

  Future<int> updateUser(Map<String, dynamic> user) async {
    final db = await database;
    return await db.update(
      'users',
      prepareForDb(user),
      where: 'id = ?',
      whereArgs: [user['id']],
    );
  }

  Future<int> deleteUser(String id) async {
    final db = await database;
    return await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }

  Future<Map<String, dynamic>?> getUserById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getUserByUsername(String username) async {
    final db = await database;
    List<Map<String, dynamic>> results = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
    );
    return results.isNotEmpty ? results.first : null;
  }

  // Package operations
  Future<List<Map<String, dynamic>>> getPackages() async {
    final db = await database;
    return await db.query('packages');
  }

  Future<int> insertPackage(Map<String, dynamic> package) async {
    final db = await database;
    return await db.insert('packages', prepareForDb(package));
  }

  Future<int> updatePackage(Map<String, dynamic> package) async {
    final db = await database;
    try {
      print('updatePackage data: $package');
      final prepared = prepareForDb(package);
      print('updatePackage prepared: $prepared');
      // Ensure all required fields exist and are valid
      if (!prepared.containsKey('id') || prepared['id'] == null) {
        throw Exception('Package id is required for update');
      }
      if (!prepared.containsKey('name') || prepared['name'] == null) {
        prepared['name'] = 'بدون اسم';
      }
      if (!prepared.containsKey('price') || prepared['price'] == null) {
        prepared['price'] = 0.0;
      }
      if (!prepared.containsKey('speed') || prepared['speed'] == null) {
        prepared['speed'] = '';
      } else if (prepared['speed'] is! String) {
        prepared['speed'] = prepared['speed'].toString();
      }
      if (!prepared.containsKey('durationInDays') ||
          prepared['durationInDays'] == null) {
        prepared['durationInDays'] = 0;
      }
      if (!prepared.containsKey('sellingPrice') ||
          prepared['sellingPrice'] == null) {
        prepared['sellingPrice'] = 0.0;
      }
      if (!prepared.containsKey('deviceCount') ||
          prepared['deviceCount'] == null) {
        prepared['deviceCount'] = 0;
      }
      if (!prepared.containsKey('createdAt') || prepared['createdAt'] == null) {
        prepared['createdAt'] = DateTime.now().toIso8601String();
      }
      if (!prepared.containsKey('isActive') || prepared['isActive'] == null) {
        prepared['isActive'] = 1;
      }
      if (!prepared.containsKey('notes')) prepared['notes'] = '';
      return await db.update(
        'packages',
        prepared,
        where: 'id = ?',
        whereArgs: [prepared['id']],
      );
    } catch (e) {
      print('Error updating package: $e');
      rethrow;
    }
  }

  Future<int> deletePackage(String id) async {
    final db = await database;
    return await db.delete('packages', where: 'id = ?', whereArgs: [id]);
  }

  Future<Map<String, dynamic>?> getPackageById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('packages')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  // Subscriber operations
  Future<List<Map<String, dynamic>>> getSubscribers() async {
    final db = await database;
    return await db.query('subscribers');
  }

  Future<int> insertSubscriber(Map<String, dynamic> subscriber) async {
    final db = await database;
    return await db.insert('subscribers', prepareForDb(subscriber));
  }

  Future<int> updateSubscriber(Map<String, dynamic> subscriber) async {
    final db = await database;
    return await db.update(
      'subscribers',
      prepareForDb(subscriber),
      where: 'id = ?',
      whereArgs: [subscriber['id']],
    );
  }

  Future<int> deleteSubscriber(String id) async {
    final db = await database;
    return await db.delete('subscribers', where: 'id = ?', whereArgs: [id]);
  }

  Future<Map<String, dynamic>?> getSubscriberById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('subscribers')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  // Activity log operations
  Future<List<Map<String, dynamic>>> getActivityLogs() async {
    final db = await database;
    return await db.query('activity_logs');
  }

  Future<int> insertActivityLog(Map<String, dynamic> log) async {
    final db = await database;
    return await db.insert('activity_logs', prepareForDb(log));
  }

  Future<List<Map<String, dynamic>>> getActivityLogsBySubscriber(
    String subscriberId,
  ) async {
    final db = await database;
    return await db.query(
      'activity_logs',
      where: 'subscriberId = ?',
      whereArgs: [subscriberId],
    );
  }

  // Payment record operations
  Future<List<Map<String, dynamic>>> getPaymentRecords() async {
    final db = await database;
    return await db.query('payment_records');
  }

  Future<int> insertPaymentRecord(Map<String, dynamic> record) async {
    final db = await database;
    return await db.insert('payment_records', prepareForDb(record));
  }

  Future<List<Map<String, dynamic>>> getPaymentRecordsBySubscriber(
    String subscriberId,
  ) async {
    final db = await database;
    return await db.query(
      'payment_records',
      where: 'subscriberId = ?',
      whereArgs: [subscriberId],
    );
  }

  // Message Template operations
  Future<List<Map<String, dynamic>>> getMessageTemplates() async {
    final db = await database;
    return await db.query('message_templates');
  }

  Future<int> insertMessageTemplate(Map<String, dynamic> template) async {
    final db = await database;
    return await db.insert('message_templates', prepareForDb(template));
  }

  Future<int> updateMessageTemplate(Map<String, dynamic> template) async {
    final db = await database;
    return await db.update(
      'message_templates',
      prepareForDb(template),
      where: 'id = ?',
      whereArgs: [template['id']],
    );
  }

  Future<int> deleteMessageTemplate(String id) async {
    final db = await database;
    return await db.delete(
      'message_templates',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<Map<String, dynamic>?> getMessageTemplateById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('message_templates')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> getMessageTemplatesByType(int type) async {
    final db = await database;
    return await db.query(
      'message_templates',
      where: 'type = ?',
      whereArgs: [type],
    );
  }

  // Expense Category operations
  Future<List<Map<String, dynamic>>> getExpenseCategories() async {
    final db = await database;
    return await db.query('expense_categories');
  }

  Future<int> insertExpenseCategory(Map<String, dynamic> category) async {
    final db = await database;
    return await db.insert('expense_categories', prepareForDb(category));
  }

  Future<int> updateExpenseCategory(Map<String, dynamic> category) async {
    final db = await database;
    return await db.update(
      'expense_categories',
      prepareForDb(category),
      where: 'id = ?',
      whereArgs: [category['id']],
    );
  }

  Future<int> deleteExpenseCategory(String id) async {
    final db = await database;
    return await db.delete(
      'expense_categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<Map<String, dynamic>?> getExpenseCategoryById(String id) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('expensesCategory')
          .doc(id)
          .get();

      if (doc.exists) {
        return doc.data();
      } else {
        return null;
      }
    } catch (e) {
      print('Error getting subscriber by ID: $e');
      return null;
    }
  }

  // Expense operations
  Future<List<Map<String, dynamic>>> getExpenses() async {
    final db = await database;
    return await db.query('expenses');
  }

  Future<int> insertExpense(Map<String, dynamic> expense) async {
    final db = await database;
    return await db.insert('expenses', prepareForDb(expense));
  }

  Future<List<Map<String, dynamic>>> getExpensesByCategoryId(
    String categoryId,
  ) async {
    final db = await database;
    return await db.query(
      'expenses',
      where: 'categoryId = ?',
      whereArgs: [categoryId],
    );
  }

  Future<void> deleteExpense(String expenseId) async {
    final db = await database;
    await db.delete('expenses', where: 'id = ?', whereArgs: [expenseId]);
  }

  // Database utilities
  Future<void> clearAllTables() async {
    // final db = await database;
    // await db.delete(
    //   'expenses',
    // ); // Delete expenses first due to foreign key constraint
    // await db.delete('expense_categories');
    // await db.delete('payment_records');
    // await db.delete('activity_logs');
    // await db.delete('subscribers');
    // await db.delete('packages');
    // await db.delete('message_templates');
    // await db.delete('users');
    // await db.delete('sas_servers');
    // await db.delete(
    //   'mikrotik_devices',
    // ); // Add this line to clear mikrotik_devices table
    // final db = await database;
    // await db.close(); // مهم جدًا تقفل الاتصال قبل الحذف

    final dbPath = await getDatabasesPath();
    final path = path_lib.join(dbPath, 'isp_manager.db'); // غيّر الاسم حسب ملفك

    await deleteDatabase(path);
    print('Database deleted successfully.');
  }

  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  // Utility method to prepare data for DB insertion/update (remove nulls and convert bool to int)
  Map<String, dynamic> prepareForDb(Map<String, dynamic> data) {
    final newData = <String, dynamic>{};
    data.forEach((key, value) {
      if (value is bool) {
        newData[key] = value ? 1 : 0;
      } else {
        newData[key] = value;
      }
    });
    newData.removeWhere((key, value) => value == null);
    return newData;
  }

  // Create a backup of the SQLite database file
  Future<String> createDatabaseBackup(String destinationPath) async {
    // Make sure the database is initialized
    final db = await database;

    // Close the database to ensure all data is written
    await db.close();
    _database = null;

    // Get the path to the database file
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String dbPath = path_lib.join(documentsDirectory.path, 'isp_manager.db');

    // Create a copy of the database file
    File dbFile = File(dbPath);

    // Check if destinationPath exists
    Directory destinationDir = Directory(destinationPath);
    if (!await destinationDir.exists()) {
      await destinationDir.create(recursive: true);
    }

    // Create backup file path with timestamp
    String backupFileName =
        'isp_manager_backup_${DateTime.now().millisecondsSinceEpoch}.db';
    String backupPath = path_lib.join(destinationPath, backupFileName);

    // Copy the database file to the destination
    await dbFile.copy(backupPath);

    // Reopen the database
    _database = await _initDatabase();

    return backupPath;
  }

  // Restore from a SQLite database backup file
  Future<void> restoreFromDatabaseBackup(String backupFilePath) async {
    // Validate the backup file exists and is a valid SQLite database
    File backupFile = File(backupFilePath);
    if (!await backupFile.exists()) {
      throw Exception('Backup file does not exist: $backupFilePath');
    }

    try {
      // Close the current database
      await closeDatabase();

      // Copy the backup file to replace the current database
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String dbPath = path_lib.join(documentsDirectory.path, 'isp_manager.db');
      File dbFile = File(dbPath);
      if (await dbFile.exists()) {
        await dbFile.delete();
      }
      await backupFile.copy(dbPath);

      // Reopen the database
      _database = await _initDatabase();
    } catch (e) {
      print(
        'Error restoring database backup: '
        '$e',
      );
    }
  }
}
