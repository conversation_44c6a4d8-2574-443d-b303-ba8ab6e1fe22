import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:local_auth/local_auth.dart'; // Import for local_auth
import 'package:shared_preferences/shared_preferences.dart'; // Import for SharedPreferences
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'firebase_options.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'theme.dart';
import 'services/database_service.dart';
import 'services/firebase_auth_service.dart';
import 'services/message_service.dart';
import 'services/update_service.dart'; // Import the new update service
import 'services/device_subscription_service.dart'; // Import the new device subscription service
import 'services/firebase_messaging_service.dart'; // Import Firebase Messaging service
import 'services/firebase_in_app_messaging_service.dart'; // Import Firebase In-App Messaging service
import 'models/device_subscription_model.dart'; // Import the device subscription model
import 'package:isp_manager/pages/firebase_auth_page.dart';
import 'pages/dashboard_page.dart';
import 'pages/renew_subscription_page.dart'; // Import RenewSubscriptionPage

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with the correct options
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('==== [MAIN] Firebase initialized successfully ====');
  } catch (e) {
    print('==== [MAIN] Firebase initialization failed: $e ====');
  }

  // طلب إذن الإشعارات مباشرة بعد تهيئة Firebase
  try {
    final messaging = FirebaseMessaging.instance;
    await messaging.requestPermission();
    print('==== [MAIN] Notification permission requested ====');
    // يمكنك الاشتراك في موضوع عام إذا أردت
    await messaging.subscribeToTopic('all');
    print('==== [MAIN] Subscribed to topic: all ====');
  } catch (e) {
    print('==== [MAIN] Error requesting notification permission or subscribing to topic: $e ====');
  }

  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://hflualxkcpsyjkujhnql.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhmbHVhbHhrY3BzeWprdWpobnFsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzEyMzIsImV4cCI6MjA2NDYwNzIzMn0.2t77DlL4UnaW-Ls8tFA8OKkURI9Rb2kpwt-4xsJHNbk',
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نظام إدارة مشتركي الإنترنت',
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      home: const AppInitializer(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar'), Locale('en')],
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}
 late DeviceSubscriptionService deviceSubscriptionService; // Declare service
class _AppInitializerState extends State<AppInitializer> {
  bool _isInitializing = true;
  bool _isLoggedIn = false;
  bool _isSubscriptionExpired = false; // New state variable
  DeviceSubscription? _deviceSubscription; // New state variable
  final LocalAuthentication _localAuth = LocalAuthentication();
 
  final FirebaseAuthService _authService =
      FirebaseAuthService(); // Add Firebase auth service

  @override
  void initState() {
    super.initState();
    _initializeApp();
    _setupAuthStateListener();
  }

  void _setupAuthStateListener() {
    // مستمع لتغييرات حالة المصادقة
    _authService.authStateChanges.listen((firebase_auth.User? user) {
      print('🔄 تغيير في حالة المصادقة: ${user?.email ?? 'لا يوجد مستخدم'}');
      if (mounted) {
        setState(() {
          _isLoggedIn = user != null;
        });
      }
    });
  }

  Future<void> _initializeApp() async {
    try {
      final dbService = DatabaseService();
      final messageService = MessageService();
      final prefs = await SharedPreferences.getInstance();
      deviceSubscriptionService = DeviceSubscriptionService(
        Supabase.instance.client,
        prefs,
      );

      // Initialize Firebase Messaging Service
      final firebaseMessagingService = FirebaseMessagingService();
      await firebaseMessagingService.initialize();

      // Initialize Firebase In-App Messaging Service
      final firebaseInAppMessagingService = FirebaseInAppMessagingService();
      await firebaseInAppMessagingService.initialize();
      
      // Trigger app open event for in-app messaging
      await firebaseInAppMessagingService.triggerAppOpen();

      // Migrate to SQLite if needed
      await dbService.migrateToSQLite();

      // Initialize database with sample data
      await dbService.initializeDatabase();

      // Create default message templates if they don't exist
      await messageService.createDefaultTemplates();

      // التحقق المحسن من حالة المصادقة
      _isLoggedIn = await _authService.isUserAuthenticated();
      final firebaseUser = _authService.currentUser;

      print('🔍 حالة المصادقة المحسنة: $_isLoggedIn');
      print('👤 المستخدم الحالي: ${firebaseUser?.email}');

      // تحقق من خيار تذكرني فقط إذا كان المستخدم مسجل دخول
      if (_isLoggedIn) {
        final bool rememberMe = prefs.getBool('remember_me') ?? true; // افتراضياً true
        final String? savedEmail = prefs.getString('user_email');
        final String? loginTimestamp = prefs.getString('login_timestamp');

        print('💾 خيار تذكرني: $rememberMe');
        print('📧 البريد المحفوظ: $savedEmail');
        print('⏰ وقت آخر تسجيل دخول: $loginTimestamp');

        if (!rememberMe) {
          // إذا لم يكن remember_me مفعّلًا، سجّل خروج المستخدم
          print('🚪 تسجيل خروج تلقائي لأن تذكرني غير مفعل');
          await _authService.signOut();
          await dbService.setCurrentUser(null);
          // مسح البيانات المحفوظة
          await prefs.remove('user_email');
          await prefs.remove('login_timestamp');
          _isLoggedIn = false;
        }
      }

      if (_isLoggedIn) {
        // Check if user data exists in Firestore
        final userData = await _authService.getUserData(firebaseUser!.uid);
        if (userData != null) {
          // Store user data in local database for compatibility
          await dbService.setCurrentUser(userData);
        }

        final prefs = await SharedPreferences.getInstance();
        final bool isBiometricEnabled = prefs.getBool('isBiometricEnabled') ?? false;

        if (isBiometricEnabled) {
          final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
          final List<BiometricType> availableBiometrics = await _localAuth.getAvailableBiometrics();

          if (canCheckBiometrics && availableBiometrics.isNotEmpty) {
            final bool didAuthenticate = await _localAuth.authenticate(
              localizedReason: 'يرجى المصادقة للدخول إلى التطبيق',
              options: const AuthenticationOptions(
                stickyAuth: true,
                biometricOnly: true,
              ),
            );

            if (!didAuthenticate) {
              // If biometric authentication fails, sign out from Firebase
              await _authService.signOut();
              await dbService.setCurrentUser(null);
              _isLoggedIn = false;
              if (!mounted) return;
              // Show error message before navigating
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('فشل المصادقة البيومترية. يرجى تسجيل الدخول.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          } else {
            // Biometric was enabled but no biometrics available, disable it
            await prefs.setBool('isBiometricEnabled', false);
            if (!mounted) return;
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تعطيل قفل التطبيق بالبصمة/الوجه لعدم توفرها على جهازك.'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }

      // Register or fetch device subscription with automatic migration support
      print('🔍 فحص الاشتراك مع دعم الترحيل التلقائي...');
      _deviceSubscription = await deviceSubscriptionService.registerOrFetchDeviceSubscription(
        firebase_auth.FirebaseAuth.instance.currentUser?.email ?? ""
      );

      // Get server time for accurate subscription check
      DateTime? serverTime = await deviceSubscriptionService.fetchServerTime();
      if (_deviceSubscription == null ||
          !_deviceSubscription!.isActive ||
          serverTime == null ||
          _deviceSubscription!.subscriptionEndDate.isBefore(serverTime)) {
        _isSubscriptionExpired = true;
        print('❌ انتهت صلاحية الاشتراك أو غير نشط (فحص وقت الخادم).');
      } else {
        print('✅ الاشتراك نشط. رقم الحساب: ${_deviceSubscription!.accountNumber}, ينتهي في: ${_deviceSubscription!.subscriptionEndDate}');
      }

      setState(() {
        _isInitializing = false;
      });

      // Perform update check after initialization
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkUpdate();
      });
    } catch (e) {
      print('Error initializing app: $e');
      setState(() {
        _isInitializing = false;
      });
    }
  }

  // New widget for subscription expired
  Widget _buildSubscriptionExpiredWidget() {
    return Scaffold(
      backgroundColor: Colors.orange.shade50,
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.lock_clock, size: 80, color: Colors.orange),
              SizedBox(height: 16),
              Text(
                'الاشتراك منتهي الصلاحية',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              SizedBox(height: 12),
              Text(
                'انتهت صلاحية اشتراكك. يرجى تجديد الاشتراك للمتابعة.',
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24),
              // Optionally add a button to contact support or go to a renewal page
              ElevatedButton.icon(
                onPressed: () {
                  if (_deviceSubscription != null) {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => RenewSubscriptionPage(
                          deviceSubscription: _deviceSubscription!,
                        ),
                      ),
                    );
                  }
                },
                icon: Icon(Icons.refresh, color: Colors.white),
                label: Text(
                  'تجديد الاشتراك',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _checkUpdate() async {
    try {
      print('بدء فحص التحديث الإجباري...');
      final updateService = UpdateService();

      // طباعة تحليل مفصل للمطور
      await updateService.printDetailedAnalysis();

      final bool updateRequired = await updateService.isUpdateRequired();

      print('نتيجة فحص التحديث النهائية: $updateRequired');

      if (updateRequired) {
        print('يتوفر تحديث - عرض نافذة التحديث');
        final latestVersionData = await updateService.fetchLatestVersion();
        if (latestVersionData != null) {
          // إظهار نافذة التحديث وحجب باقي التطبيق
          await updateService.showForcedUpdateDialog(context);

          // بعد إظهار نافذة التحديث، نتحقق دورياً إذا كان التحديث ما زال مطلوباً
          _startPeriodicUpdateCheck();
        } else {
          print('خطأ: لا توجد بيانات تحديث متاحة');
        }
      } else {
        print('لا يوجد تحديث مطلوب - السماح بالوصول للتطبيق');
      }
    } catch (e) {
      print('خطأ في فحص التحديث: $e');
    }
  }

  // فحص دوري للتحديث (كل 30 ثانية) للتأكد من عدم تخطي التحديث
  void _startPeriodicUpdateCheck() {
    Timer.periodic(Duration(seconds: 30), (timer) async {
      try {
        final updateService = UpdateService();
        final bool stillNeedsUpdate = await updateService.isUpdateRequired();

        if (stillNeedsUpdate) {
          print('التحديث ما زال مطلوباً - إعادة عرض النافذة');
          if (mounted && context.mounted) {
            await updateService.showForcedUpdateDialog(context);
          }
        } else {
          print('تم إلغاء التحديث الإجباري');
          timer.cancel();
        }
      } catch (e) {
        print('خطأ في الفحص الدوري: $e');
      }
    });
  }

  // دالة للتحقق من حالة التحديث الإجباري وحجب التطبيق
  Widget _buildUpdateBlockingWidget() {
    return Scaffold(
      backgroundColor: Colors.red.shade50,
      body: Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withOpacity(0.2),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.block, size: 80, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'التطبيق محجوب',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              SizedBox(height: 12),
              Text(
                'يجب تحديث التطبيق للمتابعة',
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () async {
                  final updateService = UpdateService();
                  await updateService.showForcedUpdateDialog(context);
                },
                icon: Icon(Icons.update, color: Colors.white),
                label: Text(
                  'عرض خيارات التحديث',
                  style: TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
                Theme.of(context).colorScheme.surface,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
                SizedBox(height: 24),
                Text(
                  'جاري تهيئة التطبيق...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'فحص التحديثات...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // فحص دوري للتحديث الإجباري وحجب التطبيق إذا لزم الأمر
    return FutureBuilder<bool>(
      future: _checkIfUpdateStillRequired(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(body: Center(child: CircularProgressIndicator()));
        }

        if (snapshot.data == true) {
          // إذا كان التحديث مطلوب، عرض شاشة الحجب
          return _buildUpdateBlockingWidget();
        }

        // Check subscription status
        if (_isSubscriptionExpired) {
          return _buildSubscriptionExpiredWidget();
        }

        // إذا لم يكن التحديث مطلوب، عرض التطبيق الطبيعي
        return MaterialApp(
          title: 'نظام إدارة مشتركي الإنترنت',
          theme: lightTheme,
          darkTheme: darkTheme,
          themeMode: ThemeMode.system,
          debugShowCheckedModeBanner: false,
          home: _isLoggedIn ? const DashboardPage() : const FirebaseAuthPage(),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar'), Locale('en')],
        );
      },
    );
  }

  // التحقق من حالة التحديث المطلوب
  Future<bool> _checkIfUpdateStillRequired() async {
    try {
      final updateService = UpdateService();
      return await updateService.isUpdateRequired();
    } catch (e) {
      print('خطأ في فحص حالة التحديث: $e');
      return false; // في حالة الخطأ، السماح بالوصول للتطبيق
    }
  }
}
