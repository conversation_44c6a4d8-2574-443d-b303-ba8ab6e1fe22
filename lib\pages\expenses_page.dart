import 'package:flutter/material.dart';
import 'dart:async';
import '../services/database_service.dart';
import '../services/app_settings_service.dart';
import '../widgets/currency_country_widgets.dart';
import 'expense_log_page.dart'; // تأكد من استيراد صفحة سجل المصاريف
import 'expense_categories_page.dart'; // تأكد من استيراد صفحة إدارة الفئات
import 'add_expense_page.dart'; // تأكد من استيراد صفحة إضافة مصروف
import '../services/firebase_auth_service.dart';
import '../models/user_model.dart';

class ExpensesPage extends StatefulWidget {
  const ExpensesPage({super.key});

  @override
  State<ExpensesPage> createState() => _ExpensesPageState();
}

class _ExpensesPageState extends State<ExpensesPage> {
  double _currentBalance = 0.0;
  double _totalCollected = 0.0;
  double _totalExpenses = 0.0;
  bool _isLoading = true;
  String _currencySymbol = 'د.ع';
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadUser();
    _loadFinancialSummary();
    _loadCurrencySymbol();
  }

  Future<void> _loadUser() async {
    final authService = FirebaseAuthService();
    final firebaseUser = authService.currentUser;
    if (firebaseUser != null) {
      final userData = await authService.getUserData(firebaseUser.uid);
      setState(() {
        _currentUser = userData;
      });
    }
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await AppSettingsService.getCurrencySymbol();
    setState(() {
      _currencySymbol = symbol;
    });
  }

  Future<void> _loadFinancialSummary() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final collected = await DatabaseService().getTotalCollectedAmount();
      await DatabaseService().syncPaymentsToFirebase();
      final expenses = await DatabaseService().getTotalExpensesAmount();
      setState(() {
        _totalCollected = collected;
        _totalExpenses = expenses;
        _currentBalance = collected - expenses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        // Handle error, maybe show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الملخص المالي: $e')),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!(_currentUser?.hasPermission(Permission.viewExpenses) ?? false)) {
      return Scaffold(
        appBar: AppBar(title: const Text('المصاريف والإيرادات')),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : const Center(child: Text('ليس لديك صلاحية لعرض المصاريف')),
      );
    }

    return Scaffold(
      appBar: AppBar(title: const Text('المصاريف والإيرادات')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadFinancialSummary,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Card(
                      elevation: 4,
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            const Text(
                              'الرصيد الحالي',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 10),
                            CurrencyText(
                              amount: _currentBalance,
                              style: TextStyle(
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                                color: _currentBalance >= 0
                                    ? Colors.green.shade700
                                    : Colors.red.shade700,
                              ),
                            ),
                            const SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Column(
                                  children: [
                                    const Text(
                                      'إجمالي المقبوضات',
                                      style: TextStyle(fontSize: 16),
                                    ),
                                    const SizedBox(height: 4),
                                    CurrencyText(
                                      amount: _totalCollected,
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                                Column(
                                  children: [
                                    const Text(
                                      'إجمالي المصاريف',
                                      style: TextStyle(fontSize: 16),
                                    ),
                                    const SizedBox(height: 4),
                                    CurrencyText(
                                      amount: _totalExpenses,
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.orange.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    _buildActionButton(
                      context,
                      icon: Icons.category,
                      label: 'إدارة فئات المصاريف',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ExpenseCategoriesPage(),
                          ),
                        );
                      },
                    ),
                    _buildActionButton(
                      context,
                      icon: Icons.add_circle_outline,
                      label: 'إضافة مصروف جديد',
                      onPressed: () async {
                        if (!(_currentUser?.hasPermission(
                              Permission.addExpenses,
                            ) ??
                            false)) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('ليس لديك صلاحية لإضافة مصروف'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AddExpensePage(),
                          ),
                        );
                        if (result == true) {
                          _loadFinancialSummary();
                        }
                      },
                    ),
                    _buildActionButton(
                      context,
                      icon: Icons.list_alt,
                      label: 'سجل المصاريف',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ExpenseLogPage(),
                          ),
                        );
                      },
                    ),
                    _buildActionButton(
                      context,
                      icon: Icons.balance,
                      label: 'مطابقة الحسابات',
                      onPressed: () => _showReconciliationDialog(),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Future<void> _showReconciliationDialog() async {
    final TextEditingController collectedController = TextEditingController(
      text: _totalCollected.toStringAsFixed(0),
    );
    final TextEditingController expensesController = TextEditingController(
      text: _totalExpenses.toStringAsFixed(0),
    );
    final TextEditingController notesController = TextEditingController();

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('مطابقة الحسابات المالية'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'يمكنك تعديل القيم التالية لمطابقة الحسابات مع الواقع:',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Card(
                  color: Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'القيم الحالية:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Row(
                          children: [
                            const Text('إجمالي المقبوضات: '),
                            CurrencyText(amount: _totalCollected),
                          ],
                        ),
                        Row(
                          children: [
                            const Text('إجمالي المصاريف: '),
                            CurrencyText(amount: _totalExpenses),
                          ],
                        ),
                        Row(
                          children: [
                            const Text('الرصيد الحالي: '),
                            CurrencyText(amount: _currentBalance),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: collectedController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'إجمالي المقبوضات الجديد',
                    suffixText: _currencySymbol,
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: expensesController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'إجمالي المصاريف الجديد',
                    suffixText: _currencySymbol,
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    hintText: 'أضف أي ملاحظات حول سبب المطابقة...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newCollected = double.parse(collectedController.text);
                final newExpenses = double.parse(expensesController.text);
                final notes = notesController.text.trim();

                // Validate inputs
                if (newCollected < 0 || newExpenses < 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('لا يمكن أن تكون القيم سالبة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Show loading indicator with better error handling
                bool isLoading = true;
                late Timer timeoutTimer;

                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => WillPopScope(
                    onWillPop: () async => !isLoading,
                    child: const Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text(
                            'جاري مطابقة الحسابات...',
                            style: TextStyle(color: Colors.white),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'قد تستغرق هذه العملية دقيقة واحدة',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );

                // Set up timeout timer
                timeoutTimer = Timer(const Duration(seconds: 60), () {
                  if (isLoading) {
                    isLoading = false;
                    Navigator.of(context).pop(); // Hide loading indicator
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('انتهت مهلة العملية - حاول مرة أخرى'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  }
                });

                try {
                  // Execute reconciliation
                  await DatabaseService().reconcileFinancialData(
                    newTotalCollected: newCollected,
                    newTotalExpenses: newExpenses,
                    notes: notes.isNotEmpty ? notes : null,
                  );

                  timeoutTimer.cancel();
                  if (isLoading) {
                    isLoading = false;
                    Navigator.of(context).pop(); // Hide loading indicator

                    // Refresh the financial summary
                    await _loadFinancialSummary();

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تمت مطابقة الحسابات بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                  Navigator.of(
                    context,
                  ).pop(true); // Pop the reconciliation dialog on success
                } catch (e) {
                  timeoutTimer.cancel();
                  if (isLoading) {
                    isLoading = false;
                    Navigator.of(context).pop(); // Hide loading indicator
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'خطأ في مطابقة الحسابات: ${e.toString()}',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                  Navigator.of(
                    context,
                  ).pop(false); // Pop the reconciliation dialog on error
                }
              },
              child: const Text('تأكيد المطابقة'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(icon, size: 30, color: Theme.of(context).primaryColor),
              const SizedBox(width: 16),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              const Icon(Icons.arrow_forward_ios, size: 20, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}
